<?php

namespace App\Events;

use App\Models\ImportFile;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ScriptStatusChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $importFiles;
    public $message;
    public $redirectUrl;
    public $user;
    public $count;

    /**
     * Create a new event instance.
     *
     * @param array|Collection $importFiles The import files that had their status changed
     * @param User $user The user who changed the status
     * @return void
     */
    public function __construct($importFiles, User $user)
    {
        $this->importFiles = $importFiles;
        $this->user = $user;
        $this->count = count($importFiles);

        // Get the first import file to determine the redirect URL
        $firstImportFile = $importFiles[0] ?? null;

        // Simplified message with only provider name and script count
        $this->message = "{$user->first_name} {$user->last_name} has sent {$this->count} script(s) for approval";
        $this->redirectUrl = '/scripts/ready-to-send/approval';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        $channelName = config('broadcasting.connections.pusher.channels.scripts');
        return new Channel($channelName);
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'script.status.changed';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'count' => $this->count,
            'user_name' => "{$this->user->first_name} {$this->user->last_name}",
            'user_id' => $this->user->id,
            'user_role' => $this->user->role,
            'message' => $this->message,
            'redirect_url' => $this->redirectUrl,
            'timestamp' => now()->toDateTimeString(),
        ];
    }
}
