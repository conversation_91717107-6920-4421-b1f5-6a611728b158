<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use App\Services\LogService;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * The user has been authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return mixed
     */
    protected function authenticated(Request $request, $user)
    {
        // Check if user is inactive (is_active = 0)
        if (env('APP_ENV') != 'local') {
            if (is_on_staff_portal()) {
                if (!($user->role == User::ROLE_ADMIN || $user->role == User::ROLE_OPERATOR)) {
                    $this->guard()->logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();
                    // Throw validation exception with custom message
                    throw ValidationException::withMessages([
                        $this->username() => ['Login with the provided email and password failed. Please check your details and try again.'],
                    ]);
                }
            } else {
                if ($user->role != User::ROLE_PROVIDER) {
                    $this->guard()->logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();
                    // Throw validation exception with custom message
                    throw ValidationException::withMessages([
                        $this->username() => ['Login with the provided email and password failed. Please check your details and try again.'],
                    ]);
                }
            }
        };

        if ($user->is_active == 1) {
            // Log the user out
            $this->guard()->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            // Throw validation exception with custom message
            throw ValidationException::withMessages([
                $this->username() => ['Your account is inactive. Please contact the administrator.'],
            ]);
        }

        if (is_null($user->password_changed_at)) {
            $user->is_password_reset = false;
            $user->save();
        }

        // Log successful login
        LogService::logLogin($user);

        return redirect()->intended($this->redirectPath());
    }

    /**
     * The user has been logged out.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    protected function loggedOut(Request $request)
    {
        // Get the user before they are logged out
        $user = $request->user();

        if ($user) {
            // Log the logout
            LogService::logLogout($user);
        }

        return redirect('/login');
    }
}
