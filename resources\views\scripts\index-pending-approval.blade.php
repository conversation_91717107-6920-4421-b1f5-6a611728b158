@extends('master')

@php
use App\Models\ImportFile;
@endphp

@section('content')
<div class="card card-custom mb-5">

    <div class="card-body" x-data="{ showFilter: false }">

        <div class="row justify-content-between ">
            <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                <div class="input-icon">
                    <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                    <span>
                        <i class="flaticon2-search-1 text-muted"></i>
                    </span>
                </div>
            </div>
            <div class="col-auto">
                <button type="button" id="download-all-global-btn" class="btn btn-dark">
                    <i class="fa fa-download mr-1"></i> Download All
                </button>
                <button type="button" id="download-selected-global-btn" class="btn btn-dark">
                    <i class="fa fa-download mr-1"></i> Download Selected
                </button>
                <button type="button" id="send-all-global-btn" class="btn btn-dark">
                    <i class="fa fa-paper-plane mr-1"></i> Send All for Approval
                </button>
                <button type="button" id="send-selected-global-btn" class="btn btn-dark">
                    <i class="fa fa-paper-plane mr-1"></i> Send Selected for Approval
                </button>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="medication_filter">Medication:</label>
                <select class="form-control" id="medication_filter">
                    <option value="">All Medications</option>
                    @foreach ($medications as $medication)
                    <option value="{{ $medication->id }}">{{ $medication->name }}</option>
                    @endforeach
                </select>
            </div>
        </div>

        <div class="datatable datatable-bordered datatable-head-custom" id="pending_import_files_search"></div>

    </div>
</div>
@endsection
@section('styles')
<style>
    button:disabled {
        cursor: not-allowed !important;
    }
</style>
@endsection
@section('scripts')
<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
<script>
    // Make datatable globally accessible for notifications to refresh it
    window.datatable = null;
    var datatableElement;
    var searchElement;
    var columnArray;

    // Store selected IDs globally to preserve them during page navigation
    var globalSelectedIds = [];

    const storagePath = `{{ url('/storage') }}`;
    const apiRoute = `{{ route('scripts.api.ready-to-send') }}`;
    let url = "{{ Storage::url('/') }}";
    const sendAllRoute = `{{ route('scripts.send-for-approval') }}`;


    datatableElement = $('#pending_import_files_search');
    searchElement = $('#users_search');

    columnArray = [{
            field: 'checkbox',
            title: '<label class="checkbox checkbox-single checkbox-all"><input type="checkbox" id="select-all-checkbox" />&nbsp;<span></span></label>',
            sortable: false,
            width: 55,
            autoHide: false,
            textAlign: 'center',
            template: function(data) {
                return `<label class="checkbox checkbox-single">
                    <input type="checkbox" class="row-checkbox" value="${data.id}" />&nbsp;<span></span>
                </label>`;
            }
        },
        {
            field: 'import_file_name',
            title: `File Name`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return `<div style="white-space: normal; text-wrap: wrap;">${data.import_file_name ?? ''}</div>`;
            }
        },
        {
            field: 'created_at',
            title: `Created At`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
            }
        },
        {
            field: 'signed_at',
            title: `Signed at`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.signed_at ? moment(data.signed_at).format('MM/DD/YYYY hh:mm A') :
                    '<b>Not Signed Yet</b>';
            }
        },
        {
            field: 'sent_at',
            title: `Sent at`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return data.sent_at ? moment(data.sent_at).format('MM/DD/YYYY hh:mm A') :
                    '<b>Not Sent Yet</b>';
            }
        },

        {
            field: 'script_date',
            title: `Script date`,
            width: 'auto',
            sortable: true,
            autoHide: false,
            template: function(data) {
                return moment(data.script_date).format('MM/DD/YYYY');
            }
        },
        {
            field: 'last_name',
            title: `Last name`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'first_name',
            title: `First name`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'medication',
            title: `Medication`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },

        {
            field: 'status',
            title: `Status`,
            width: 'auto',
            sortable: true,
            autoHide: false
        },
        {
            field: 'Actions',
            title: 'Actions',
            sortable: false,
            width: 'auto',
            overflow: 'visible',
            autoHide: false,
            template: function(row) {
                const downloadRoute = `{{ route('archive.file-download', ['id' => '::ID']) }}`.replace('::ID',
                    row.id);
                const viewRoute = `{{ route('archive.show-pdf', ['id' => '::ID']) }}`.replace('::ID', row.id);
                // const sendRoute = `{{ route('archive.sign-pdf', ['id' => '::ID']) }}`.replace('::ID', row.id);

                const status = row.status.toLowerCase();

                return `
                        <a href="${downloadRoute}" class="btn btn-sm btn-clean btn-icon" title="Download">
                            <i class="menu-icon fas fa-download"></i>
                        </a>
                        <a href="${viewRoute}" class="btn btn-sm btn-clean btn-icon" title="View" target="_blank">
                            <i class="menu-icon fas fa-eye"></i>
                        </a>
                    `;
            },
        }

    ];

    // datatable = datatableElement.KTDatatable({
    //     data: {
    //         type: 'remote',
    //         source: {
    //             read: {
    //                 url: apiRoute,
    //                 //sample custom headers
    //                 headers: {
    //                     'X-CSRF-TOKEN': '{{ csrf_token() }}'
    //                 },
    //                 map: function(raw) {
    //                     // sample data mapping
    //                     var dataSet = raw;
    //                     if (typeof raw.data !== 'undefined') {
    //                         dataSet = raw.data;
    //                     }
    //                     return dataSet;
    //                 },
    //             },
    //         },
    //         pageSize: 10,
    //         serverPaging: true,
    //         serverFiltering: true,
    //         serverSorting: true,
    //     },
    //     layout: {
    //         scroll: false,
    //         footer: false
    //     },
    //     sortable: true,
    //     pagination: true,
    //     search: {
    //         input: searchElement,
    //         key: 'search'
    //     },
    //     columns: columnArray
    // });
    window.datatable = datatableElement.KTDatatable({
        data: {
            type: 'remote',
            source: {
                read: {
                    url: apiRoute,
                    //sample custom headers
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    params: function() {
                        // Get the current query parameters
                        const query = datatable.getDataSourceQuery();

                        // Get the current search value directly from the search input
                        const searchValue = $(searchElement).val() || '';

                        // Add selected IDs and filter parameters to the request
                        return {
                            displayed_ids: globalSelectedIds,
                            medication_id: query.medication_id || '',
                            // Get the current search value
                            search: searchValue,
                            // Add query structure for compatibility with backend
                            query: {
                                medication_id: query.medication_id || '',
                                search: searchValue
                            }
                        };
                    },
                    map: function(raw) {
                        // sample data mapping
                        var dataSet = raw;
                        if (typeof raw.data !== 'undefined') {
                            dataSet = raw.data;
                        }
                        return dataSet;
                    },
                },
            },
            pageSize: 10,
            serverPaging: true,
            serverFiltering: true,
            serverSorting: true,
        },
        pagination: true,
        search: {
            input: searchElement,
            key: 'search'
        },
        layout: {
            customScrollbar: false,
            scroll: true,
        },
        columns: columnArray
    });

    // Function to disable/enable all buttons on the page
    function togglePageButtons(disable) {
        // Disable/enable only the "All" buttons
        $('#download-all-global-btn').prop('disabled', disable);
        $('#send-all-global-btn').prop('disabled', disable);

        // If disabling all buttons due to empty data, also disable the "Selected" buttons
        if (disable) {
            toggleSelectedButtons(true);
        }
    }

    // Function to toggle the "Selected" buttons based on whether any items are selected
    function toggleSelectedButtons(disable) {
        $('#download-selected-global-btn').prop('disabled', disable);
        $('#send-selected-global-btn').prop('disabled', disable);
    }

    // Initialize the "Selected" buttons as disabled by default
    toggleSelectedButtons(true);

    // Add event handler for medication filter
    $('#medication_filter').on('change', function() {
        // Get current filter value
        const medicationId = $('#medication_filter').val();

        // Get current search value - this will preserve the search text when changing filters
        const searchValue = $(searchElement).val() || '';

        // Set the query parameters for the datatable
        datatable.setDataSourceQuery({
            medication_id: medicationId,
            search: searchValue,
            query: {
                medication_id: medicationId,
                search: searchValue
            }
        });

        // Disable all buttons while loading
        togglePageButtons(true);
        toggleSelectedButtons(true);

        // Reload the datatable with the new query parameters
        datatable.reload();
    });

    // Initialize the datatable query parameters with empty values
    datatable.setDataSourceQuery({
        query: {
            medication_id: '',
            search: ''
        }
    });

    // Handle ajax done event
    window.datatable.on('datatable-on-ajax-done', function(e, data) {
        // Check if data is empty
        const isEmpty = !data || !data.length;
        togglePageButtons(isEmpty);

        // Update the "Select All" checkbox state and button states after data is loaded
        setTimeout(function() {
            // Restore checkboxes for items that were previously selected
            restoreSelectedCheckboxes();

            // Update the "Select All" checkbox state
            updateSelectAllCheckboxState();

            // Update button states based on selection
            toggleSelectedButtons(globalSelectedIds.length === 0);
        }, 100);
    });

    // Handle ajax fail event
    window.datatable.on('datatable-on-ajax-fail', function(e, jqXHR) {
        // Disable buttons on error
        togglePageButtons(true);
    });

    // Function to capture client device time and submit the sign form
    function captureTimeAndSign(signRoute) {
        // Get the current date
        const now = new Date();

        // Create a complete date-time string with timezone information
        // Format: YYYY-MM-DD HH:MM:SS +/-HHMM
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        // Get timezone offset in minutes and convert to hours and minutes
        const tzOffset = now.getTimezoneOffset();
        const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
        const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
        const tzSign = tzOffset <= 0 ? '+' : '-'; // Note: getTimezoneOffset returns negative for positive UTC offsets

        // Construct the full datetime string with timezone
        const clientTimestamp =
            `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${tzSign}${tzOffsetHours}${tzOffsetMinutes}`;

        console.log('Using client timestamp:', clientTimestamp);

        // Create a form to submit
        const form = $('<form>', {
            method: 'GET',
            action: signRoute
        });

        // Add client timestamp as a query parameter
        form.append($('<input>', {
            type: 'hidden',
            name: 'client_timestamp',
            value: clientTimestamp
        }));

        // Submit the form
        $('body').append(form);
        form.submit();
        form.remove();
    }

    $(document).on('click', '.sent-btn', function(e) {
        e.preventDefault();
        const sendAllRoute = $(this).data('sent-route');
        captureTimeAndSign(sendAllRoute);
    });

    const routeTemplate = "{{ route('scripts.download-all-pdf') }}";

    $('#download-all-global-btn').on('click', function() {
        const form = $('<form>', {
            method: 'POST',
            action: routeTemplate
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '{{ csrf_token() }}'
        }));

        // Determine the status based on the API route being used
        const statusValue = apiRoute.includes('ready-to-send') ? '{{ ImportFile::STATUS_SIGNED }}' : '{{ ImportFile::STATUS_PENDING_APPROVAL }}';

        form.append($('<input>', {
            type: 'hidden',
            name: 'status',
            value: statusValue // Dynamically set based on the API route
        }));

        // Get current filter values and add them to the form
        const medicationId = $('#medication_filter').val();
        if (medicationId) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'medication_id',
                value: medicationId
            }));
        }

        // Get current search value
        const searchValue = $(searchElement).val();
        if (searchValue) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'search',
                value: searchValue
            }));
        }

        $('body').append(form);
        form.submit();
        form.remove();
    });

    $('#download-selected-global-btn').on('click', function() {
        // Use our global selected IDs
        if (globalSelectedIds.length === 0) {
            return;
        }

        const form = $('<form>', {
            method: 'POST',
            action: routeTemplate.replace('__ID__', '') // same endpoint
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '{{ csrf_token() }}'
        }));

        // Determine the status based on the API route being used
        const statusValue = apiRoute.includes('ready-to-send') ? '{{ ImportFile::STATUS_SIGNED }}' : '{{ ImportFile::STATUS_PENDING_APPROVAL }}';

        // Pass status filter
        form.append($('<input>', {
            type: 'hidden',
            name: 'status',
            value: statusValue // Dynamically set based on the API route
        }));

        // Add displayed_ids[] inputs
        globalSelectedIds.forEach(function(id) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'displayed_ids[]',
                value: id
            }));
        });

        $('body').append(form);
        form.submit();
        form.remove();
    });

    $('#send-all-global-btn').on('click', function() {
        const form = $('<form>', {
            method: 'POST',
            action: sendAllRoute
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '{{ csrf_token() }}'
        }));

        // Determine the status based on the API route being used
        const currentStatus = apiRoute.includes('ready-to-send') ? '{{ ImportFile::STATUS_SIGNED }}' : '{{ ImportFile::STATUS_PENDING_APPROVAL }}';
        const targetStatus = apiRoute.includes('ready-to-send') ? '{{ ImportFile::STATUS_PENDING_APPROVAL }}' : '{{ ImportFile::STATUS_SENT }}';

        // Pass current status
        form.append($('<input>', {
            type: 'hidden',
            name: 'status',
            value: currentStatus // Dynamically set based on the API route
        }));

        // Pass target status
        form.append($('<input>', {
            type: 'hidden',
            name: 'changed_status',
            value: targetStatus // Dynamically set based on the API route
        }));

        $('body').append(form);
        form.submit();
        form.remove();
    });

    $('#send-selected-global-btn').on('click', function() {
        // Use our global selected IDs
        if (globalSelectedIds.length === 0) {
            return;
        }

        const form = $('<form>', {
            method: 'POST',
            action: sendAllRoute
        });

        form.append($('<input>', {
            type: 'hidden',
            name: '_token',
            value: '{{ csrf_token() }}'
        }));

        // Determine the status based on the API route being used
        const currentStatus = apiRoute.includes('ready-to-send') ? '{{ ImportFile::STATUS_SIGNED }}' : '{{ ImportFile::STATUS_PENDING_APPROVAL }}';
        const targetStatus = apiRoute.includes('ready-to-send') ? '{{ ImportFile::STATUS_PENDING_APPROVAL }}' : '{{ ImportFile::STATUS_SENT }}';

        // Pass current status
        form.append($('<input>', {
            type: 'hidden',
            name: 'status',
            value: currentStatus // Dynamically set based on the API route
        }));

        // Pass target status
        form.append($('<input>', {
            type: 'hidden',
            name: 'changed_status',
            value: targetStatus // Dynamically set based on the API route
        }));

        // Add displayed_ids[] inputs
        globalSelectedIds.forEach(function(id) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'displayed_ids[]',
                value: id
            }));
        });

        $('body').append(form);
        form.submit();
        form.remove();
    });

    // Handle "Select All" checkbox
    $(document).on('change', '#select-all-checkbox', function() {
        let isChecked = $(this).is(':checked');

        // Check/uncheck all visible checkboxes
        $('.row-checkbox').prop('checked', isChecked);

        // Use our improved getSelectedIds function to update the global selection state
        getSelectedIds();

        // Update button states based on selection
        toggleSelectedButtons(globalSelectedIds.length === 0);
    });

    // Handle individual checkbox changes
    $(document).on('change', '.row-checkbox', function() {
        // Use our improved getSelectedIds function to update the global selection state
        getSelectedIds();

        // Update the "Select All" checkbox state
        updateSelectAllCheckboxState();
    });

    // Function to update the "Select All" checkbox state
    function updateSelectAllCheckboxState() {
        const totalCheckboxes = $('.row-checkbox').length;
        const checkedCheckboxes = $('.row-checkbox:checked').length;

        if (totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes) {
            $('#select-all-checkbox').prop('checked', true);
        } else {
            $('#select-all-checkbox').prop('checked', false);
        }
    }

    // Function to restore selected checkboxes based on globalSelectedIds
    function restoreSelectedCheckboxes() {
        // For each checkbox on the current page
        $('.row-checkbox').each(function() {
            const id = $(this).val();
            // If this ID is in our globalSelectedIds array, check it
            if (globalSelectedIds.includes(id)) {
                $(this).prop('checked', true);
            }
        });
    }

    // Handle pagination events to maintain selection state
    datatable.on('datatable-on-layout-updated', function() {
        // After layout update (which happens on page change), restore selections
        setTimeout(function() {
            restoreSelectedCheckboxes();
            updateSelectAllCheckboxState();
            toggleSelectedButtons(globalSelectedIds.length === 0);
        }, 100);
    });

    // Handle datatable reloads
    datatable.on('datatable-on-reloaded', function() {
        // After reload, restore selections
        setTimeout(function() {
            restoreSelectedCheckboxes();
            updateSelectAllCheckboxState();
            toggleSelectedButtons(globalSelectedIds.length === 0);
        }, 100);
    });

    // Handle page change events
    datatable.on('datatable-on-goto-page', function(e, meta) {
        // When changing pages, we need to preserve our globalSelectedIds
        // The datatable will reload data, and our ajax-done handler will restore selections
        console.log('Page changed to:', meta.page);
    });

    function getSelectedIds() {
        // Get all visible checkbox IDs on the current page
        let visibleIds = [];
        $('.row-checkbox').each(function() {
            visibleIds.push($(this).val());
        });

        // First, remove any IDs from globalSelectedIds that are visible on the current page
        // This ensures we don't have stale selections
        globalSelectedIds = globalSelectedIds.filter(function(id) {
            return !visibleIds.includes(id);
        });

        // Now add all currently checked IDs to globalSelectedIds
        $('.row-checkbox:checked').each(function() {
            const id = $(this).val();
            globalSelectedIds.push(id);
        });

        // Update button states based on selection
        toggleSelectedButtons(globalSelectedIds.length === 0);

        return globalSelectedIds;
    }
</script>
@endsection