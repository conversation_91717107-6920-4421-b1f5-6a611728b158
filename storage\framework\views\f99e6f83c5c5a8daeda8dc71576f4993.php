<div class="aside aside-left aside-fixed d-flex flex-column flex-row-auto " id="kt_aside">

    <div class="brand flex-column-auto" id="kt_brand">

        <a href="<?php echo e(route('dashboard')); ?>" class="brand-logo">
            <h3 class="m-0 text-dark"><?php echo e(config('app.name')); ?></h3>
        </a>

    </div>
    <?php
    use App\Models\User;
    use App\Models\ImportFile;
    $current_route = Route::currentRouteName();
    $currentParams = request()->query();

    // Get counts for menu items
    $user = Auth::user();
    $readyToSignCount = 0;
    $pendingApprovalCount = 0;
    $sentCount = 0;

    if ($user->role === User::ROLE_PROVIDER) {
    // For providers, only count their own files
    $readyToSignCount = ImportFile::whereHas('import', function ($q) use ($user) {
    $q->where('user_id', $user->id);
    })
    ->whereIn('status', [ImportFile::STATUS_NEW, ImportFile::STATUS_PENDING_REVISION])
    ->count();

    $pendingApprovalCount = ImportFile::whereHas('import', function ($q) use ($user) {
    $q->where('user_id', $user->id);
    })
    ->where('status', ImportFile::STATUS_PENDING_APPROVAL)
    ->count();

    $sentCount = ImportFile::whereHas('import', function ($q) use ($user) {
    $q->where('user_id', $user->id);
    })
    ->where('status', ImportFile::STATUS_SENT)
    ->count();
    } elseif ($user->role === User::ROLE_ADMIN || $user->role === User::ROLE_OPERATOR) {
    // For admin and operators, count all files
    $allCount = ImportFile::count();

    $readyToSendCount = ImportFile::whereIn('status', [
    // ImportFile::STATUS_PENDING_DISPATCH,
    ImportFile::STATUS_PENDING_APPROVAL,
    ])->count();

    $sentCount = ImportFile::where('status', ImportFile::STATUS_SENT)->count();
    }
    ?>

    <style>
    .menu-count {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        min-width: 18px;
        font-size: 10px;
        margin-left: auto;
        font-weight: bold;
        padding: 0 5px;
        position: relative;
        right: 10px;
    }

    /* Make sure the menu text doesn't wrap */
    .menu-text {
        flex: 1;
    }

    /* Make the menu link a flex container */
    .menu-link {
        display: flex !important;
        align-items: center;
    }
    </style>

    <div class="aside-menu-wrapper flex-column-fluid" id="kt_aside_menu_wrapper">

        <div id="kt_aside_menu" class="aside-menu" data-menu-vertical="1" data-menu-scroll="1"
            data-menu-dropdown-timeout="500">

            <ul class="menu-nav">

                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Dashboard','active' => 'dashboard','route' => 'dashboard','icon' => 'fa fa-home'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>

                <li class="menu-item menu-item-submenu
                    <?php if(strpos($current_route, 'scripts.') !== false || strpos(Route::currentRouteName(), 'excel') !== false): ?> menu-item-open menu-item-here <?php endif; ?>
                    ">
                    <a href="javascript:;" class="menu-link menu-toggle">
                        <i class="menu-icon fa fa-cog"></i>
                        <span class="menu-text">Scripts</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="menu-submenu">
                        <i class="menu-arrow"></i>
                        <ul class="menu-subnav">
                            <?php if(Auth::user()->role === User::ROLE_ADMIN): ?>
                            <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Bulk Import','route' => 'excel.staff-bulk-import'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.aside-item-with-count','data' => ['text' => 'All','route' => 'scripts.all','count' => $allCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item-with-count'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => 'All','route' => 'scripts.all','count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $attributes = $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $component = $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.aside-item-with-count','data' => ['text' => 'Ready to Send','route' => 'scripts.ready-to-send','count' => $readyToSendCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item-with-count'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => 'Ready to Send','route' => 'scripts.ready-to-send','count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($readyToSendCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $attributes = $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $component = $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.aside-item-with-count','data' => ['text' => 'Sent','route' => 'scripts.sent','count' => $sentCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item-with-count'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => 'Sent','route' => 'scripts.sent','count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sentCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $attributes = $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $component = $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
                            <?php elseif(Auth::user()->role === User::ROLE_PROVIDER): ?>
                            <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Add New','route' => 'excel.new-import'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Bulk Import','route' => 'excel.import'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.aside-item-with-count','data' => ['text' => 'Ready to Sign','route' => 'scripts.ready-to-sign','count' => $readyToSignCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item-with-count'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => 'Ready to Sign','route' => 'scripts.ready-to-sign','count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($readyToSignCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $attributes = $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $component = $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.aside-item-with-count','data' => ['text' => 'Pending Approval','route' => 'scripts.provider-pending-approval','count' => $pendingApprovalCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item-with-count'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => 'Pending Approval','route' => 'scripts.provider-pending-approval','count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($pendingApprovalCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $attributes = $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $component = $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.aside-item-with-count','data' => ['text' => 'Sent','route' => 'scripts.sent','count' => $sentCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item-with-count'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => 'Sent','route' => 'scripts.sent','count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sentCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $attributes = $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $component = $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
                            <?php elseif(Auth::user()->role === User::ROLE_OPERATOR): ?>
                            <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Bulk Import','route' => 'excel.staff-bulk-import'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                            
                            <?php if (isset($component)) { $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.aside-item-with-count','data' => ['text' => 'Ready to Send','route' => 'scripts.ready-to-send','count' => $readyToSendCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item-with-count'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => 'Ready to Send','route' => 'scripts.ready-to-send','count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($readyToSendCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $attributes = $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $component = $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.aside-item-with-count','data' => ['text' => 'Sent','route' => 'scripts.sent','count' => $sentCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item-with-count'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['text' => 'Sent','route' => 'scripts.sent','count' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sentCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $attributes = $__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__attributesOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b)): ?>
<?php $component = $__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b; ?>
<?php unset($__componentOriginal4cf0e6f05a49969e89d829093cdbfe9b); ?>
<?php endif; ?>
                            <?php endif; ?>
                        </ul>
                    </div>
                </li>

                <?php if(Auth::user()->role === User::ROLE_PROVIDER): ?>
                
                
                <?php endif; ?>

                <?php if(Auth::user()->role === User::ROLE_PROVIDER): ?>
                <?php
                // Count all files for archive
                $archiveCount = ImportFile::whereHas('import', function ($q) use ($user) {
                $q->where('user_id', $user->id);
                })->count();
                ?>
                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Archive','active' => 'archive','route' => 'archive.index','icon' => 'fa fa-globe-americas'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                <?php endif; ?>

                <?php if(Auth::user()->role === User::ROLE_OPERATOR || Auth::user()->role === User::ROLE_ADMIN): ?>
                
                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Providers','active' => 'users','route' => 'users.index','icon' => 'fa fa-users'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                <?php endif; ?>

                <?php if(Auth::user()->role === User::ROLE_ADMIN): ?>
                
                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Staff','active' => 'admin','route' => 'admin.index','icon' => 'fa fa-user'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                <?php endif; ?>
                <!--<?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Universities','active' => 'universities','route' => 'dashboard','icon' => 'fa fa-city'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Services','active' => 'services','route' => 'dashboard','icon' => 'fa fa-briefcase'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Clients','active' => 'clients','route' => 'dashboard','icon' => 'fa fa-users'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Admins','active' => 'admins','route' => 'dashboard','icon' => 'fa fa-users'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Home Slider','active' => 'home-slider','route' => 'dashboard','icon' => 'fa fa-images'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?> -->

                <li class="menu-item menu-item-submenu
                    <?php if(strpos($current_route, 'settings.') !== false): ?> menu-item-open menu-item-here <?php endif; ?>
                    ">
                    <a href="javascript:;" class="menu-link menu-toggle">
                        <i class="menu-icon fa fa-cog"></i>
                        <span class="menu-text">Settings</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="menu-submenu">
                        <i class="menu-arrow"></i>
                        <ul class="menu-subnav">

                            <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Change Password','route' => 'settings.change-password'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                            <?php if(Auth::user()->role === User::ROLE_ADMIN): ?>
                            <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Fax Options','route' => 'settings.fax-options'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                                <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Logs','route' => 'settings.logs'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                            <?php endif; ?>

                            <!-- <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Terms & Conditions','route' => 'settings.terms.index'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'Privacy Policy','route' => 'settings.privacy.index'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginal4108c28821d5131464c68e22c40e838c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4108c28821d5131464c68e22c40e838c = $attributes; } ?>
<?php $component = App\View\Components\AsideItem::resolve(['text' => 'About Us','route' => 'settings.about.index'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('aside-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AsideItem::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $attributes = $__attributesOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__attributesOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4108c28821d5131464c68e22c40e838c)): ?>
<?php $component = $__componentOriginal4108c28821d5131464c68e22c40e838c; ?>
<?php unset($__componentOriginal4108c28821d5131464c68e22c40e838c); ?>
<?php endif; ?> -->

                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/layouts/aside.blade.php ENDPATH**/ ?>