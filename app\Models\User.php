<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    const
        ROLE_ADMIN = 'administrator',
        ROLE_OPERATOR = 'operator',
        ROLE_PROVIDER = 'provider';


    //TABLE
    public $table = 'users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    //FILLABLES
    protected $fillable = [
        'first_name',
        'email',
        'role',
        'password',
        'last_name',
        'printed_name',
        'clinic_name',
        'NPI#',
        'LIC#',
        'DEA#',
        'phone',
        'fax',
        'address',
        'city',
        'state_id',
        'zip',
        'signature',
        'is_active',
        'password_changed_at',
        'is_password_reset'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    //HIDDEN
    protected $hidden = [
        'email_verified_at',
        // 'password',
        'remember_token',
    ];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    //CASTS
    protected $casts = [
        'name' => 'string',
        'email' => 'string',
        'email_verified_at' => 'datetime',
        'password' => 'string',
        'remember_token' => 'string',
        'password_changed_at' => 'datetime',
    ];


    //RULES

    //RELATIONSHIPS
    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function imports()
    {
        return $this->hasMany(Import::class);
    }

    //ATTRIBUTES
    // public function getExampleAttribute()
    // {
    // }
}
