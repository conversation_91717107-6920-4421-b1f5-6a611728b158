@extends('master')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    {{-- <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Excel Import</h5>
                        <a href="{{ isset($has_back) ? $has_back : route('excel.import') }}"
                            class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left mr-1"></i> Back
                        </a>
                    </div> --}}

                    <div class="card-body">
                        @if (session('status'))
                            <div class="alert" role="alert">
                                {{ session('status') }}
                            </div>
                        @endif

                        @if (session('error'))
                            <div class="alert" role="alert">
                                {{ session('error') }}
                            </div>
                        @endif

                        <!-- Progress Steps -->
                        <div class="progress-steps-container mb-5">
                            <div class="progress-line">
                                <div class="progress-line-inner" style="width: {{ ($currentStep / $totalSteps) * 100 }}%;">
                                </div>
                            </div>
                            <div class="progress-steps">
                                <div class="progress-step {{ $currentStep >= 1 ? 'active' : '' }}">
                                    <div class="step-circle">1</div>
                                    <div class="step-label">Upload</div>
                                </div>
                                <div class="progress-step {{ $currentStep >= 2 ? 'active' : '' }}">
                                    <div class="step-circle">2</div>
                                    <div class="step-label">Create</div>
                                </div>
                                <div class="progress-step {{ $currentStep >= 3 ? 'active' : '' }}">
                                    <div class="step-circle">3</div>
                                    <div class="step-label">Sign</div>
                                </div>
                                <div class="progress-step {{ $currentStep >= 4 ? 'active' : '' }}">
                                    <div class="step-circle">4</div>
                                    <div class="step-label">Done</div>
                                </div>
                            </div>
                        </div>
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3 class="card-title"></h3>
                            {{-- <h3 class="card-title">{{ $page_title }}</h3> --}}
                            <div>
                                <div class="action-buttons-container d-flex flex-wrap align-items-center justify-content-center">
                                    @if ($currentStep == 3)
                                        <button id="download-all-btn" class="btn btn-primary m-2"
                                            style="background-color: #000000; border-color: #000000;">
                                            <i class="fas fa-download"></i> Download All
                                        </button>
                                        <button id="download-selected-btn" class="btn btn-primary m-2" disabled
                                            style="background-color: #000000; border-color: #000000;">
                                            <i class="fas fa-download"></i> Download Selected
                                        </button>
                                        <button id="send-all-btn" class="btn btn-primary m-2"
                                            style="background-color: #000000; border-color: #000000;">
                                            <i class="fas fa-paper-plane"></i> Sign All
                                        </button>
                                        <button id="send-selected-btn" class="btn btn-primary m-2" disabled
                                            style="background-color: #000000; border-color: #000000;">
                                            <i class="fas fa-paper-plane"></i> Sign Selected
                                        </button>
                                    @elseif ($currentStep == 4)
                                        <!-- <div class="text-center m-2">
                                            <span id="scripts-count"
                                                class="alert px-4 py-2 rounded-pill fw-semibold shadow"
                                                style="display: inline-block; font-size: 1.5rem;">
                                                {{ isset($scriptsCount) ? $scriptsCount : '' }} scripts {{ $statusText ?? 'pending approval' }}
                                            </span>
                                        </div> -->
                                        <button id="download-all-btn" class="btn btn-primary m-2"
                                            style="background-color: #000000; border-color: #000000;">
                                            <i class="fas fa-download"></i> Download All
                                        </button>
                                        <button id="download-selected-btn" class="btn btn-primary m-2" disabled
                                            style="background-color: #000000; border-color: #000000;">
                                            <i class="fas fa-download"></i> Download Selected
                                        </button>
                                        <a href="{{ url('/excel-import') }}" class="btn btn-primary m-2"
                                            style="background-color: #000000; border-color: #000000;">
                                            <i class="fas fa-list"></i> Back to Home
                                        </a>
                                    @endif
                                </div>

                            </div>
                        </div>

                        <div class="card-body">
                            @if (session('status'))
                                <div class="alert" role="alert">
                                    {{ session('status') }}
                                </div>
                            @endif

                            @if (session('error'))
                                <div class="alert" role="alert">
                                    {{ session('error') }}
                                </div>
                            @endif

                            @if (isset($processedRowCount) && isset($skippedRowCount))
                                <div class="alert" role="alert">
                                    <strong>Processing Results:</strong> {{ $processedRowCount }} rows processed
                                    successfully,
                                    <span style="color: red">{{ $skippedRowCount }} rows with errors were skipped.</span>
                                </div>
                            @endif

                            @if ($currentStep == 4)
                                <div class="alert" role="alert">
                                    <span id="scripts-count">
                                        {{ isset($scriptsCount) ? $scriptsCount : '' }} scripts {{ $statusText ?? 'pending approval' }}
                                    </span>
                                </div>
                            @endif


                            <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-4">
                                <div class="input-icon">
                                    <input type="text" class="form-control" placeholder="Search..."
                                        id="imports_search" />
                                    <span>
                                        <i class="flaticon2-search-1 text-muted"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="datatable datatable-bordered datatable-head-custom" id="imports_dt"></div>

                            <!-- We'll use the top buttons for navigation instead of bottom buttons -->
                        </div>
                    </div>

                    <style>
                        .card {
                            border: none;
                            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
                            border-radius: 0;
                        }

                        .card-header {
                            background-color: #fff;
                            border-bottom: 1px solid #f1f1f1;
                            padding: 15px 20px;
                        }

                        /* Progress Steps Styling */
                        .progress-steps-container {
                            position: relative;
                            padding: 20px 0;
                            margin: 0 auto;
                            max-width: 700px;
                        }

                        .progress-steps {
                            display: flex;
                            justify-content: space-between;
                            position: relative;
                            z-index: 1;
                        }

                        .progress-step {
                            text-align: center;
                            width: 20%;
                            position: relative;
                        }

                        .step-circle {
                            width: 36px;
                            height: 36px;
                            border-radius: 50%;
                            background-color: #e9ecef;
                            color: #6c757d;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin: 0 auto 8px;
                            font-weight: 500;
                            font-size: 14px;
                        }

                        .progress-step.active .step-circle {
                            background-color: #000000;
                            color: white;
                        }

                        .step-label {
                            font-size: 13px;
                            color: #6c757d;
                            font-weight: 400;
                        }

                        .progress-step.active .step-label {
                            color: #000000;
                            font-weight: 500;
                        }

                        .progress-line {
                            position: absolute;
                            top: 38px;
                            left: 10%;
                            right: 10%;
                            height: 2px;
                            background-color: #e9ecef;
                            z-index: 0;
                        }

                        .progress-line-inner {
                            height: 100%;
                            background-color: #000000;
                        }

                        .btn-danger {
                            background-color: #000000;
                            border-color: #000000;
                        }

                        @media (max-width: 768px) {
                            .action-buttons-container .btn {
                                padding: 0.375rem 0.75rem;
                                /* Reduced padding */
                                font-size: 0.85rem;
                                /* Smaller font size */
                                width: 100%;
                                /* Full width for stacking */
                                margin-bottom: 0.5rem;
                                /* Space between stacked buttons */
                            }

                            .action-buttons-container {
                                flex-direction: column;
                                /* Stack buttons vertically */
                                align-items: stretch;
                                /* Make them take full width */
                            }
                        }

                        /* Simple style for clickable rows */
                        #imports_dt tbody tr td {
                            cursor: pointer;
                        }

                        /* Highlight on hover */
                        #imports_dt tbody tr:hover td {
                            background-color: rgba(54, 153, 255, 0.1) !important;
                        }

                        /* Custom alert styling */
                        .alert {
                            background-color: #dff9ff;
                            color: #333;
                            border: none;
                            padding: 15px;
                            border-radius: 5px;
                            margin-bottom: 15px;
                        }
                    </style>

                    <!-- Preview Modal -->
                    <div class="modal fade" id="previewModal" tabindex="-1" role="dialog"
                        aria-labelledby="previewModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="previewModalLabel">Prescription Preview</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <div id="prescription-preview">
                                        <div class="text-center">
                                            <div class="spinner-border" role="status">
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                    <a href="#" id="download-modal-pdf" class="btn btn-primary">Download PDF</a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endsection

                @push('scripts')
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                    <script>
                        $(document).ready(function() {
                            // Preview button click handler
                            $('.preview-btn').on('click', function(e) {
                                e.preventDefault();

                                const fileId = $(this).data('file-id');
                                const downloadUrl = "{{ route('excel.download-pdf') }}/" + fileId;

                                // Set the download button URL
                                $('#download-modal-pdf').attr('href', downloadUrl);

                                // Show the modal
                                $('#previewModal').modal('show');

                                // Load the prescription preview
                                $('#prescription-preview').html(
                                    '<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>'
                                );

                                // Here you would typically load the preview via AJAX
                                // For simplicity, we'll just show a message
                                setTimeout(function() {
                                    $('#prescription-preview').html(
                                        '<div class="alert">Preview functionality requires additional server-side implementation.</div>'
                                    );
                                }, 1000);
                            });
                        });
                    </script>

                    <script>
                        var datatable;
                        var datatableElement;
                        var searchElement;
                        var columnArray;
                        // Store selected IDs to preserve them during search
                        var selectedIds = [];
                        // Flag to track if we're currently in a search
                        var isSearchActive = false;

                        const storagePath = `{{ url('/storage') }}`;
                        const apiRoute =
                            `{{ route('prescription-list.api', ['importId' => $import_id ?? null]) }}?status={{ $statusText ?? '' }}`;
                        let url = "{{ Storage::url('/') }}";

                        datatableElement = $('#imports_dt');
                        searchElement = $('#imports_search');

                        columnArray = [{
                                field: 'Select',
                                title: '<label class="checkbox">Select all &nbsp; <input type="checkbox" id="select-all-checkbox" /><span></span></label>',
                                sortable: false,
                                width: 100,
                                autoHide: false,
                                template: function(row) {
                                    return `<label class="checkbox">
                        <input type="checkbox" class="prescription-checkbox" data-id="${row.id}" />
                        <span></span>
                    </label>`;
                                }
                            },
                            {
                                field: 'number',
                                title: `#`,
                                width: 50,
                                autoHide: false,
                            },
                            {
                                field: 'script_date',
                                title: `Script Date`,
                                width: 100,
                                autoHide: false,
                                template: function(row) {
                                    return moment(row.script_date).format('MM/DD/YYYY');
                                }
                            },
                            {
                                field: 'last_name',
                                title: `Last Name`,
                                width: 100,
                                autoHide: false,
                            },
                            {
                                field: 'first_name',
                                title: `First Name`,
                                width: 100,
                                autoHide: false,
                            },
                            {
                                field: 'medication',
                                title: `Medication`,
                                width: 100,
                                autoHide: false,
                            },

                            // {
                            //     field: 'stregnth',
                            //     title: `Stregnth`,
                            //     width: 100,
                            //     autoHide: false,
                            // },
                            // {
                            //     field: 'dosing',
                            //     title: `Dosing`,
                            //     width: 70,
                            //     autoHide: false,
                            // },
                            // {
                            //     field: 'file_name',
                            //     title: `File Name`,
                            //     width: 130,
                            //     autoHide: false,
                            // },
                            {
                                field: 'status',
                                title: `status`,
                                width: 130,
                                autoHide: false,
                            },
                            //         {
                            //             field: 'Actions',
                            //             title: 'Actions',
                            //             sortable: false,
                            //             width: 130,
                            //             overflow: 'visible',
                            //             autoHide: false,
                            //             template: function(row) {
                            //                 return `<a href="{{ route('excel.download-pdf') }}/${row.id}" class="btn btn-danger btn-sm">
        //     <i class="fas fa-file-pdf"></i> Download PDF
        // </a>`;
                            //             }
                            //         }
                        ];

                        datatable = datatableElement.KTDatatable({
                            data: {
                                type: 'remote',
                                source: {
                                    read: {
                                        url: apiRoute,
                                        //sample custom headers
                                        headers: {
                                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                        },
                                        map: function(raw) {
                                            // sample data mapping
                                            var dataSet = raw;
                                            if (typeof raw.data !== 'undefined') {
                                                dataSet = raw.data;
                                            }

                                            // Update the scripts count with the total count of all records (not filtered)
                                            if (raw && raw.meta && typeof raw.meta.total_all !== 'undefined') {
                                                const count = raw.meta.total_all;
                                                const statusText = '{{ $statusText ?? 'signed' }}';
                                                $('#scripts-count').text(count + ' scripts ' + statusText);
                                            }

                                            return dataSet;
                                        },
                                    },
                                },
                                pageSize: 10,
                                serverPaging: true,
                                serverFiltering: true,
                                serverSorting: true,
                            },
                            layout: {
                                scroll: false,
                                footer: false
                            },
                            sortable: true,
                            pagination: true,
                            search: {
                                input: searchElement,
                                key: 'search'
                            },
                            columns: columnArray
                        });

                        // Function to update button states based on selection
                        function updateButtonStates() {
                            const selectedCount = $('.prescription-checkbox:checked').length;

                            // Enable/disable the selected buttons based on selection count
                            if (selectedCount > 0) {
                                // Enable "Selected" buttons
                                $('#download-selected-btn').prop('disabled', false);
                                $('#send-selected-btn').prop('disabled', false);

                                // Disable "All" buttons when items are selected
                                $('#download-all-btn').prop('disabled', true);
                                $('#send-all-btn').prop('disabled', true);
                            } else {
                                // Disable "Selected" buttons
                                $('#download-selected-btn').prop('disabled', true);
                                $('#send-selected-btn').prop('disabled', true);

                                // Enable "All" buttons when no items are selected
                                $('#download-all-btn').prop('disabled', false);
                                $('#send-all-btn').prop('disabled', false);
                            }

                            // Update the count display if it exists
                            if ($('#selected-count').length) {
                                $('#selected-count').text(selectedCount);
                            }
                        }

                        // Function to save selected IDs
                        function saveSelectedIds() {
                            // Get all currently visible checkboxes
                            const visibleCheckboxes = $('.prescription-checkbox');

                            // For each visible checkbox, update the selection state in our array
                            visibleCheckboxes.each(function() {
                                const id = parseInt($(this).data('id'), 10);
                                const isChecked = $(this).prop('checked');

                                if (id) {
                                    // If checked, add to selectedIds if not already there
                                    if (isChecked && !selectedIds.includes(id)) {
                                        selectedIds.push(id);
                                    }
                                    // If unchecked, remove from selectedIds if it's there
                                    else if (!isChecked && selectedIds.includes(id)) {
                                        selectedIds = selectedIds.filter(selectedId => selectedId !== id);
                                    }
                                }
                            });

                            console.log('Updated selected IDs:', selectedIds);
                        }

                        // Function to restore selected IDs
                        function restoreSelectedIds() {
                            console.log('Restoring selections for IDs:', selectedIds);

                            // First uncheck all checkboxes
                            $('.prescription-checkbox').prop('checked', false);

                            // Then check only those that were previously selected
                            let restoredCount = 0;
                            $('.prescription-checkbox').each(function() {
                                const id = parseInt($(this).data('id'), 10);
                                if (id && selectedIds.includes(id)) {
                                    $(this).prop('checked', true);
                                    restoredCount++;
                                }
                            });

                            // Update the "Select All" checkbox state
                            const totalCheckboxes = $('.prescription-checkbox').length;
                            const checkedCheckboxes = $('.prescription-checkbox:checked').length;

                            if (totalCheckboxes === checkedCheckboxes && totalCheckboxes > 0) {
                                $('#select-all-checkbox').prop('checked', true);
                            } else {
                                $('#select-all-checkbox').prop('checked', false);
                            }

                            // Update button states
                            updateButtonStates();
                            console.log('Restored ' + restoredCount + ' selections out of ' + selectedIds.length + ' saved IDs');
                        }

                        // Add event listeners to search input
                        $(searchElement).on('keyup search input', function(e) {
                            // Track if we're in a search
                            isSearchActive = $(this).val() !== '';

                            // Disable all buttons while searching
                            if (isSearchActive) {
                                // Temporarily disable all buttons during search
                                $('#download-all-btn, #download-selected-btn, #send-all-btn, #send-selected-btn').prop('disabled', true);
                            }

                            // If search is cleared, restore selections after a delay
                            if (!isSearchActive) {
                                console.log('Search cleared, will restore selections');
                                setTimeout(restoreSelectedIds, 500);
                            }
                        });

                        // Set up event handlers for datatable events
                        datatable.on('kt-datatable--on-ajax-done', function(event, data) {
                            if (data && data.meta && typeof data.meta.total_all !== 'undefined') {
                                // Update the scripts count with the total count of all records (not filtered)
                                const count = data.meta.total_all;
                                const statusText = '{{ $statusText ?? 'signed' }}';
                                $('#scripts-count').text(count + ' scripts ' + statusText);

                                // If no data is found, disable all buttons
                                if (count === 0) {
                                    $('#download-all-btn, #download-selected-btn, #send-all-btn, #send-selected-btn').prop('disabled', true);
                                }
                            }

                            // After data is loaded, restore selections
                            setTimeout(restoreSelectedIds, 300);
                        });

                        // Handle datatable reloads
                        datatable.on('kt-datatable--on-reloaded', function() {
                            console.log('Datatable reloaded, restoring selections');
                            // After reload, restore selections with a delay
                            setTimeout(restoreSelectedIds, 300);
                        });

                        // Handle datatable layout updates
                        datatable.on('datatable-on-layout-updated', function() {
                            console.log('Datatable layout updated');

                            // After layout update, restore selections
                            setTimeout(restoreSelectedIds, 300);

                            $('.preview-btn').on('click', function(e) {
                                e.preventDefault();

                                const fileId = $(this).data('file-id');
                                const downloadUrl = "{{ route('excel.download-pdf') }}/" + fileId;

                                // Set the download button URL
                                $('#download-modal-pdf').attr('href', downloadUrl);

                                // Show the modal
                                $('#previewModal').modal('show');

                                // Load the prescription preview
                                $('#prescription-preview').html(
                                    '<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>'
                                );

                                // Here you would typically load the preview via AJAX
                                // For simplicity, we'll just show a message
                                setTimeout(function() {
                                    $('#prescription-preview').html(
                                        '<div class="alert">Preview functionality requires additional server-side implementation.</div>'
                                    );
                                }, 1000);
                            });
          
                            // Handle "Select All" checkbox
                            $('#select-all-checkbox').off('change').on('change', function() {
                                const isChecked = $(this).prop('checked');
                                $('.prescription-checkbox').prop('checked', isChecked);

                                // Save the selection state
                                saveSelectedIds();

                                // Update button states
                                updateButtonStates();

                                // If we have checkboxes and they're all checked, disable "All" buttons
                                if (isChecked && $('.prescription-checkbox').length > 0) {
                                    $('#download-all-btn').prop('disabled', true);
                                    $('#send-all-btn').prop('disabled', true);
                                    $('#download-selected-btn').prop('disabled', false);
                                    $('#send-selected-btn').prop('disabled', false);
                                } else if (!isChecked) {
                                    // If unchecked, enable "All" buttons and disable "Selected" buttons
                                    $('#download-all-btn').prop('disabled', false);
                                    $('#send-all-btn').prop('disabled', false);
                                    $('#download-selected-btn').prop('disabled', true);
                                    $('#send-selected-btn').prop('disabled', true);
                                }
                            });

                            // Handle individual checkbox selection
                            $('.prescription-checkbox').off('change').on('change', function() {
                                // Save the selection state
                                saveSelectedIds();

                                // Update button states
                                updateButtonStates();

                                // Update "Select All" checkbox state
                                const totalCheckboxes = $('.prescription-checkbox').length;
                                const checkedCheckboxes = $('.prescription-checkbox:checked').length;

                                if (totalCheckboxes === checkedCheckboxes && totalCheckboxes > 0) {
                                    $('#select-all-checkbox').prop('checked', true);
                                } else {
                                    $('#select-all-checkbox').prop('checked', false);
                                }
                            });

                            // Add row click handler for checkbox selection
                            $('#imports_dt tbody tr td').off('click').on('click', function(e) {
                                // Skip if clicking on the checkbox cell
                                if ($(this).is(':first-child') ||
                                    $(e.target).is('input[type="checkbox"]') ||
                                    $(e.target).closest('a').length ||
                                    $(e.target).closest('button').length ||
                                    $(e.target).closest('i').length) {
                                    return;
                                }

                                // Find the checkbox in the first cell
                                const checkbox = $(this).closest('tr').find('td:first-child input[type="checkbox"]');

                                // Toggle the checkbox
                                checkbox.prop('checked', !checkbox.prop('checked'));

                                // Trigger change event
                                checkbox.trigger('change');
                            });

                            // Restore selections after layout is updated
                            restoreSelectedIds();
                        });

                        // Handle download all button
                        $('#download-all-btn').on('click', function() {
                            // Get the current status from the URL or page variable
                            const currentStatus = '{{ $statusText ?? 'created' }}';
                            const importId = '{{ $import_id }}';

                            // Simplify our approach - just get IDs from the DOM
                            const visibleIds = [];

                            // Try to get IDs from the DOM
                            $('.prescription-checkbox').each(function() {
                                const id = $(this).data('id');
                                if (id) {
                                    visibleIds.push(parseInt(id, 10));
                                }
                            });

                            console.log('Visible IDs from DOM:', visibleIds);

                            // Create a form to submit
                            const form = $('<form>', {
                                'method': 'POST',
                                'action': '{{ route('excel.download-all-pdf', ['importId' => $import_id]) }}'
                            });

                            // Add CSRF token
                            form.append($('<input>', {
                                'type': 'hidden',
                                'name': '_token',
                                'value': '{{ csrf_token() }}'
                            }));

                            // Add status parameter
                            form.append($('<input>', {
                                'type': 'hidden',
                                'name': 'status',
                                'value': currentStatus
                            }));

                            // If we found IDs, include them
                            if (visibleIds.length > 0) {
                                visibleIds.forEach(id => {
                                    form.append($('<input>', {
                                        'type': 'hidden',
                                        'name': 'displayed_ids[]',
                                        'value': id
                                    }));
                                });
                            } else {
                                // If no IDs found, use the all_with_status flag
                                form.append($('<input>', {
                                    'type': 'hidden',
                                    'name': 'all_with_status',
                                    'value': 'true'
                                }));
                            }

                            // Add to body and submit
                            $('body').append(form);
                            form.submit();
                            form.remove();
                        });

                        // Handle download selected button
                        $('#download-selected-btn').on('click', function() {
                            const selectedIds = getSelectedIds();
                            if (selectedIds.length === 0) return;

                            // Create a form to submit the selected IDs
                            const form = $('<form>', {
                                'method': 'POST',
                                'action': '{{ route('excel.download-selected-pdf') }}'
                            });

                            // Add CSRF token
                            form.append($('<input>', {
                                'type': 'hidden',
                                'name': '_token',
                                'value': '{{ csrf_token() }}'
                            }));

                            // Add selected IDs
                            selectedIds.forEach(id => {
                                form.append($('<input>', {
                                    'type': 'hidden',
                                    'name': 'ids[]',
                                    'value': id
                                }));
                            });

                            // Add to body and submit
                            $('body').append(form);
                            form.submit();
                            form.remove();
                        });

                        // Handle send all button (directly to Pending Approval)
                        $('#send-all-btn').on('click', function() {
                            // Get all visible prescription IDs from the table
                            const visibleIds = [];
                            $('.prescription-checkbox').each(function() {
                                const id = $(this).data('id');
                                if (id) {
                                    visibleIds.push(parseInt(id, 10));
                                }
                            });

                            // Make sure we have a valid import_id
                            let importId = '{{ $import_id ?? '' }}';

                            // If import_id is missing, try to get it from the first visible record
                            if (!importId && visibleIds.length > 0) {
                                // We'll use the first visible ID to get its import_id via AJAX
                                $.ajax({
                                    url: '{{ route('prescription.get-import-id') }}',
                                    method: 'POST',
                                    data: {
                                        _token: '{{ csrf_token() }}',
                                        id: visibleIds[0]
                                    },
                                    dataType: 'json',
                                    async: false, // Make this synchronous so we get the import_id before proceeding
                                    success: function(response) {
                                        if (response.success && response.import_id) {
                                            importId = response.import_id;
                                            console.log('Retrieved import_id:', importId);
                                        }
                                    }
                                });
                            }

                            if (!importId) {
                                if (typeof toastr !== 'undefined') {
                                    toastr.error('Import ID is missing. Please try again or contact support.');
                                }

                                // Create a more detailed error message
                                const errorMessage = $('<div>', {
                                    'class': 'alert mt-3',
                                    'html': `
                                        <h4>Error</h4>
                                        <p>Import ID is missing. This is required to process your request.</p>
                                        <p>Please try again or contact support if the issue persists.</p>
                                    `
                                });

                                // Insert the error message at the top of the page
                                $('.card-body').first().prepend(errorMessage);

                                // Scroll to the top of the page
                                $('html, body').animate({
                                    scrollTop: 0
                                }, 'slow');

                                return;
                            }

                            // If we have an import_id, use it
                            if (importId) {
                                updateStatus(importId, null, 'Pending Approval');
                            }
                            // Otherwise, send the visible IDs directly
                            else if (visibleIds.length > 0) {
                                // Prepare the data object
                                const data = {
                                    _token: '{{ csrf_token() }}',
                                    status: 'Pending Approval',
                                    record_ids: visibleIds
                                };

                                // Disable all buttons to prevent multiple submissions
                                $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                    .prop('disabled', true);

                                // Create and show a loading overlay
                                const loadingOverlay = $('<div>', {
                                    'class': 'loading-overlay',
                                    'css': {
                                        'position': 'fixed',
                                        'top': 0,
                                        'left': 0,
                                        'width': '100%',
                                        'height': '100%',
                                        'background-color': 'rgba(0, 0, 0, 0.7)',
                                        'z-index': 9999,
                                        'display': 'flex',
                                        'justify-content': 'center',
                                        'align-items': 'center',
                                        'flex-direction': 'column'
                                    }
                                });

                                const spinner = $('<div>', {
                                    'class': 'spinner-border text-light',
                                    'role': 'status',
                                    'css': {
                                        'width': '3rem',
                                        'height': '3rem'
                                    }
                                }).append($('<span>', {
                                    'class': 'sr-only',
                                    'text': 'Loading...'
                                }));

                                const loadingText = $('<div>', {
                                    'class': 'mt-3 text-light text-center',
                                    'css': {
                                        'font-size': '1.2rem',
                                        'font-weight': 'bold',
                                        'max-width': '80%'
                                    },
                                    'html': 'Sending prescriptions...'
                                });

                                loadingOverlay.append(spinner).append(loadingText);
                                $('body').append(loadingOverlay);

                                // Show loading indicator in toastr as well
                                if (typeof toastr !== 'undefined') {
                                    toastr.info('Sending prescriptions...');
                                }

                                // Make the AJAX call
                                $.ajax({
                                    url: '{{ route('prescription.update-status') }}',
                                    method: 'POST',
                                    data: data,
                                    dataType: 'json',
                                    timeout: 300000, // 5 minute timeout for large batches
                                    success: function(response) {
                                        console.log('Response:', response);

                                        // Remove the loading overlay
                                        $('.loading-overlay').remove();

                                        if (response.success) {
                                            if (typeof toastr !== 'undefined') {
                                                toastr.success(response.message || 'Status updated successfully');
                                            }

                                            // Redirect to the appropriate page
                                            if (response.redirect) {
                                                window.location.href = response.redirect;
                                            } else {
                                                window.location.reload();
                                            }
                                        } else {
                                            // Re-enable buttons if there was an error
                                            $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                                .prop('disabled', false);

                                            if (typeof toastr !== 'undefined') {
                                                toastr.error(response.message || 'Failed to update status');
                                            }

                                            // Create a more detailed error message
                                            const errorMessage = $('<div>', {
                                                'class': 'alert mt-3',
                                                'html': `
                                                    <h4>Error</h4>
                                                    <p>${response.message || 'Failed to update status'}</p>
                                                    <p>Please try again or contact support if the issue persists.</p>
                                                `
                                            });

                                            // Insert the error message at the top of the page
                                            $('.card-body').first().prepend(errorMessage);

                                            // Scroll to the top of the page
                                            $('html, body').animate({
                                                scrollTop: 0
                                            }, 'slow');
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        // Remove the loading overlay
                                        $('.loading-overlay').remove();

                                        // Re-enable buttons
                                        $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                            .prop('disabled', false);

                                        console.error('Error details:', xhr.responseText);

                                        if (typeof toastr !== 'undefined') {
                                            toastr.error('An error occurred while updating status: ' + error);
                                        }

                                        // Create a more detailed error message
                                        const errorMessage = $('<div>', {
                                            'class': 'alert mt-3',
                                            'html': `
                                                <h4>Error</h4>
                                                <p>An error occurred while processing your request.</p>
                                                <p>Please try again or contact support if the issue persists.</p>
                                            `
                                        });

                                        // Insert the error message at the top of the page
                                        $('.card-body').first().prepend(errorMessage);

                                        // Scroll to the top of the page
                                        $('html, body').animate({
                                            scrollTop: 0
                                        }, 'slow');
                                    }
                                });
                            } else {
                                if (typeof toastr !== 'undefined') {
                                    toastr.error('No records found to process');
                                }
                            }
                        });

                        // Handle send selected button
                        $('#send-selected-btn').on('click', function() {
                            const selectedIds = getSelectedIds();
                            if (selectedIds.length === 0) {
                                if (typeof toastr !== 'undefined') {
                                    toastr.error('Please select at least one prescription to set as Pending Approval');
                                }
                                return;
                            }

                            // Get the import_id
                            const importId = '{{ $import_id ?? '' }}';

                            // If we have an import_id, use it
                            if (importId) {
                                updateStatus(importId, selectedIds, 'Pending Approval');
                            }
                            // Otherwise, send the selected IDs directly
                            else {
                                // Prepare the data object
                                const data = {
                                    _token: '{{ csrf_token() }}',
                                    status: 'Pending Approval',
                                    ids: selectedIds // Changed from record_ids to ids to match controller expectations
                                };

                                // Disable all buttons to prevent multiple submissions
                                $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                    .prop('disabled', true);

                                // Create and show a loading overlay
                                showLoadingOverlay('Sent');

                                // Make the AJAX call
                                $.ajax({
                                    url: '{{ route('prescription.update-status') }}',
                                    method: 'POST',
                                    data: data,
                                    dataType: 'json',
                                    timeout: 300000, // 5 minute timeout for large batches
                                    success: function(response) {
                                        console.log('Response:', response);

                                        // Remove the loading overlay
                                        $('.loading-overlay').remove();

                                        if (response.success) {
                                            if (typeof toastr !== 'undefined') {
                                                toastr.success(response.message || 'Status updated successfully');
                                            }

                                            // Redirect to the appropriate page
                                            if (response.redirect) {
                                                window.location.href = response.redirect;
                                            } else {
                                                window.location.reload();
                                            }
                                        } else {
                                            // Re-enable buttons if there was an error
                                            $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                                .prop('disabled', false);

                                            if (typeof toastr !== 'undefined') {
                                                toastr.error(response.message || 'Failed to update status');
                                            }

                                            // Create a more detailed error message
                                            const errorMessage = $('<div>', {
                                                'class': 'alert mt-3',
                                                'html': `
                                                    <h4>Error</h4>
                                                    <p>${response.message || 'Failed to update status'}</p>
                                                    <p>Please try again or contact support if the issue persists.</p>
                                                `
                                            });

                                            // Insert the error message at the top of the page
                                            $('.card-body').first().prepend(errorMessage);

                                            // Scroll to the top of the page
                                            $('html, body').animate({
                                                scrollTop: 0
                                            }, 'slow');
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        // Remove the loading overlay
                                        $('.loading-overlay').remove();

                                        // Re-enable buttons
                                        $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                            .prop('disabled', false);

                                        console.error('Error details:', xhr.responseText);

                                        if (typeof toastr !== 'undefined') {
                                            toastr.error('An error occurred while updating status: ' + error);
                                        }

                                        // Create a more detailed error message
                                        const errorMessage = $('<div>', {
                                            'class': 'alert mt-3',
                                            'html': `
                                                <h4>Error</h4>
                                                <p>An error occurred while processing your request.</p>
                                                <p>Please try again or contact support if the issue persists.</p>
                                            `
                                        });

                                        // Insert the error message at the top of the page
                                        $('.card-body').first().prepend(errorMessage);

                                        // Scroll to the top of the page
                                        $('html, body').animate({
                                            scrollTop: 0
                                        }, 'slow');
                                    }
                                });
                            }
                        });

                        // Helper function to get selected IDs
                        function getSelectedIds() {
                            // First save the current selection state to ensure it's up to date
                            saveSelectedIds();
                            console.log('Getting selected IDs:', selectedIds);
                            return selectedIds;
                        }

                        // Helper function to show loading overlay
                        function showLoadingOverlay(status) {
                            // Create the loading overlay
                            const loadingOverlay = $('<div>', {
                                'class': 'loading-overlay',
                                'css': {
                                    'position': 'fixed',
                                    'top': 0,
                                    'left': 0,
                                    'width': '100%',
                                    'height': '100%',
                                    'background-color': 'rgba(0, 0, 0, 0.7)',
                                    'z-index': 9999,
                                    'display': 'flex',
                                    'justify-content': 'center',
                                    'align-items': 'center',
                                    'flex-direction': 'column'
                                }
                            });

                            // Create spinner
                            const spinner = $('<div>', {
                                'class': 'spinner-border text-light',
                                'role': 'status',
                                'css': {
                                    'width': '3rem',
                                    'height': '3rem'
                                }
                            }).append($('<span>', {
                                'class': 'sr-only',
                                'text': 'Loading...'
                            }));

                            // Create loading text based on status
                            let loadingHtml = 'Setting prescriptions to Pending Approval...';
                            if (status === 'Signed') {
                                loadingHtml = `Signing prescriptions and adding signatures...<br>
                                <small class="text-warning">This may take a few minutes for large batches.<br>
                                Please do not close this window.</small>`;

                                // Add a progress indicator for signing
                                const progressContainer = $('<div>', {
                                    'class': 'mt-3',
                                    'css': {
                                        'width': '80%',
                                        'max-width': '400px'
                                    }
                                });

                                const progressBar = $('<div>', {
                                    'class': 'progress',
                                    'css': {
                                        'height': '10px',
                                        'background-color': '#444',
                                        'border-radius': '5px',
                                        'margin-top': '10px'
                                    }
                                }).append($('<div>', {
                                    'class': 'progress-bar progress-bar-striped progress-bar-animated',
                                    'role': 'progressbar',
                                    'aria-valuenow': '100',
                                    'aria-valuemin': '0',
                                    'aria-valuemax': '100',
                                    'css': {
                                        'width': '100%',
                                        'background-color': '#ea4848'
                                    }
                                }));

                                progressContainer.append(progressBar);
                                loadingOverlay.append(progressContainer);
                            }

                            const loadingText = $('<div>', {
                                'class': 'mt-3 text-light text-center',
                                'css': {
                                    'font-size': '1.2rem',
                                    'font-weight': 'bold',
                                    'max-width': '80%'
                                },
                                'html': loadingHtml
                            });

                            // Add elements to overlay and append to body
                            loadingOverlay.append(spinner).append(loadingText);
                            $('body').append(loadingOverlay);

                            // Show loading indicator in toastr as well
                            const loadingMessage = status === 'Signed' ? 'Signing prescriptions...' :
                                'Setting prescriptions to Pending Approval...';
                            if (typeof toastr !== 'undefined') {
                                toastr.info(loadingMessage);
                            } else {
                                console.log(loadingMessage);
                            }
                        }

                        // Helper function to update status with visible IDs when import_id is missing
                        function updateStatusWithVisibleIds(visibleIds, status) {
                            console.log('Updating status with visible IDs:', {
                                visible_ids: visibleIds,
                                status: status
                            });

                            // Validate the data before sending
                            if (!status) {
                                console.error('Status is required');
                                return;
                            }

                            if (!visibleIds || visibleIds.length === 0) {
                                console.error('Visible IDs are required');
                                return;
                            }

                            // Prepare the data object
                            const data = {
                                _token: '{{ csrf_token() }}',
                                status: status,
                                visible_ids: visibleIds
                            };

                            // Show loading overlay and make the AJAX call
                            showLoadingOverlay(status);

                            $.ajax({
                                url: '{{ route('prescription.update-status') }}',
                                method: 'POST',
                                data: data,
                                dataType: 'json',
                                timeout: 300000, // 5 minute timeout for large batches
                                success: handleUpdateSuccess,
                                error: handleUpdateError
                            });
                        }

                        // Helper function to update status
                        function updateStatus(importId, ids, status) {
                            // Log what we're sending for debugging
                            console.log('Updating status with:', {
                                import_id: importId,
                                ids: ids,
                                status: status
                            });

                            // Validate the data before sending
                            if (!status) {
                                console.error('Status is required');
                                return;
                            }

                            if (!importId && (!ids || ids.length === 0)) {
                                console.error('Either import ID or specific IDs are required');
                                return;
                            }

                            // Prepare the data object
                            const data = {
                                _token: '{{ csrf_token() }}',
                                status: status
                            };

                            // Add import_id only if it exists
                            if (importId) {
                                data.import_id = importId;
                            }

                            // Add ids only if they exist and are not empty
                            if (ids && ids.length > 0) {
                                data.ids = ids;
                            }

                            // Disable all buttons to prevent multiple submissions
                            $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                .prop('disabled', true);

                            // Create and show a loading overlay
                            const loadingOverlay = $('<div>', {
                                'class': 'loading-overlay',
                                'css': {
                                    'position': 'fixed',
                                    'top': 0,
                                    'left': 0,
                                    'width': '100%',
                                    'height': '100%',
                                    'background-color': 'rgba(0, 0, 0, 0.7)',
                                    'z-index': 9999,
                                    'display': 'flex',
                                    'justify-content': 'center',
                                    'align-items': 'center',
                                    'flex-direction': 'column'
                                }
                            });

                            const spinner = $('<div>', {
                                'class': 'spinner-border text-light',
                                'role': 'status',
                                'css': {
                                    'width': '3rem',
                                    'height': '3rem'
                                }
                            }).append($('<span>', {
                                'class': 'sr-only',
                                'text': 'Loading...'
                            }));

                            // Create a more detailed message for signing
                            let loadingHtml = 'Setting prescriptions to Pending Approval...';
                            if (status === 'Signed') {
                                const count = ids ? ids.length : 'all';
                                loadingHtml = `Signing ${count} prescriptions and adding signatures...<br>
                                <small class="text-warning">This may take a few minutes for large batches.<br>
                                Please do not close this window.</small>`;

                                // Add a progress indicator for signing
                                const progressContainer = $('<div>', {
                                    'class': 'mt-3',
                                    'css': {
                                        'width': '80%',
                                        'max-width': '400px'
                                    }
                                });

                                const progressBar = $('<div>', {
                                    'class': 'progress',
                                    'css': {
                                        'height': '10px',
                                        'background-color': '#444',
                                        'border-radius': '5px',
                                        'margin-top': '10px'
                                    }
                                }).append($('<div>', {
                                    'class': 'progress-bar progress-bar-striped progress-bar-animated',
                                    'role': 'progressbar',
                                    'aria-valuenow': '100',
                                    'aria-valuemin': '0',
                                    'aria-valuemax': '100',
                                    'css': {
                                        'width': '100%',
                                        'background-color': '#ea4848'
                                    }
                                }));

                                progressContainer.append(progressBar);
                                loadingOverlay.append(progressContainer);
                            }

                            const loadingText = $('<div>', {
                                'class': 'mt-3 text-light text-center',
                                'css': {
                                    'font-size': '1.2rem',
                                    'font-weight': 'bold',
                                    'max-width': '80%'
                                },
                                'html': loadingHtml
                            });

                            loadingOverlay.append(spinner).append(loadingText);
                            $('body').append(loadingOverlay);

                            // Show loading indicator in toastr as well
                            const loadingMessage = status === 'Signed' ? 'Signing prescriptions...' :
                                'Setting prescriptions to Pending Approval...';
                            if (typeof toastr !== 'undefined') {
                                toastr.info(loadingMessage);
                            } else {
                                console.log(loadingMessage);
                            }

                            $.ajax({
                                url: '{{ route('prescription.update-status') }}',
                                method: 'POST',
                                data: data,
                                dataType: 'json',
                                timeout: 300000, // 5 minute timeout for large batches
                                success: function(response) {
                                    console.log('Response:', response);

                                    // Remove the loading overlay
                                    $('.loading-overlay').remove();

                                    if (response.success) {
                                        if (typeof toastr !== 'undefined') {
                                            toastr.success(response.message || 'Status updated successfully');
                                        }

                                        // Update the count if provided in the response
                                        if (response.count !== undefined) {
                                            const statusText = '{{ $statusText ?? 'signed' }}';
                                            $('#scripts-count').text(response.count + ' scripts ' + statusText);
                                        }

                                        // Redirect to the appropriate page
                                        if (response.redirect) {
                                            window.location.href = response.redirect;
                                        } else {
                                            // Save selections before reloading
                                            saveSelectedIds();

                                            // Reload the datatable instead of the whole page
                                            datatable.reload();

                                            // Restore selections after reload
                                            setTimeout(restoreSelectedIds, 500);
                                        }
                                    } else {
                                        // Re-enable buttons if there was an error
                                        $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                            .prop('disabled', false);

                                        if (typeof toastr !== 'undefined') {
                                            toastr.error(response.message || 'Failed to update status');
                                        }
                                    }
                                },
                                error: function(xhr, status, error) {
                                    // Remove the loading overlay
                                    $('.loading-overlay').remove();

                                    // Re-enable buttons
                                    $('#download-all-btn, #download-selected-btn, #sign-all-btn, #sign-selected-btn, #send-all-btn, #send-selected-btn')
                                        .prop('disabled', false);

                                    console.error('Error details:', xhr.responseText);

                                    // Show a more helpful error message for timeouts
                                    if (status === 'timeout') {
                                        if (typeof toastr !== 'undefined') {
                                            toastr.error(
                                                'The request timed out. The server is still processing your request. Please check back in a few minutes.'
                                            );
                                        }

                                        // Create a more detailed error message
                                        const errorMessage = $('<div>', {
                                            'class': 'alert alert-danger',
                                            'html': `
                                                <h4>Request Timeout</h4>
                                                <p>The server is taking longer than expected to process your request.</p>
                                                <p>Your prescriptions are still being processed in the background. Please wait a few minutes and then:</p>
                                                <ol>
                                                    <li>Refresh this page</li>
                                                    <li>Check the "Signed" tab to see if your prescriptions have been processed</li>
                                                </ol>
                                                <p>If the prescriptions are still not signed after 5 minutes, please try again with a smaller batch.</p>
                                            `
                                        });

                                        // Insert the error message at the top of the page
                                        $('.card-body').first().prepend(errorMessage);

                                        // Scroll to the top of the page
                                        $('html, body').animate({
                                            scrollTop: 0
                                        }, 'slow');
                                    } else {
                                        if (typeof toastr !== 'undefined') {
                                            toastr.error('An error occurred while updating status: ' + error);
                                        }
                                    }
                                }
                            });
                        }
                    </script>
                @endpush
