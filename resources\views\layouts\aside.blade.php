<div class="aside aside-left aside-fixed d-flex flex-column flex-row-auto " id="kt_aside">

    <div class="brand flex-column-auto" id="kt_brand">

        <a href="{{ route('dashboard') }}" class="brand-logo">
            <h3 class="m-0 text-dark">{{ config('app.name') }}</h3>
        </a>

    </div>
    @php
    use App\Models\User;
    use App\Models\ImportFile;
    $current_route = Route::currentRouteName();
    $currentParams = request()->query();

    // Get counts for menu items
    $user = Auth::user();
    $readyToSignCount = 0;
    $pendingApprovalCount = 0;
    $sentCount = 0;

    if ($user->role === User::ROLE_PROVIDER) {
    // For providers, only count their own files
    $readyToSignCount = ImportFile::whereHas('import', function ($q) use ($user) {
    $q->where('user_id', $user->id);
    })
    ->whereIn('status', [ImportFile::STATUS_NEW, ImportFile::STATUS_PENDING_REVISION])
    ->count();

    $pendingApprovalCount = ImportFile::whereHas('import', function ($q) use ($user) {
    $q->where('user_id', $user->id);
    })
    ->where('status', ImportFile::STATUS_PENDING_APPROVAL)
    ->count();

    $sentCount = ImportFile::whereHas('import', function ($q) use ($user) {
    $q->where('user_id', $user->id);
    })
    ->where('status', ImportFile::STATUS_SENT)
    ->count();
    } elseif ($user->role === User::ROLE_ADMIN || $user->role === User::ROLE_OPERATOR) {
    // For admin and operators, count all files
    $allCount = ImportFile::count();

    $readyToSendCount = ImportFile::whereIn('status', [
    // ImportFile::STATUS_PENDING_DISPATCH,
    ImportFile::STATUS_PENDING_APPROVAL,
    ])->count();

    $sentCount = ImportFile::where('status', ImportFile::STATUS_SENT)->count();
    }
    @endphp

    <style>
    .menu-count {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        min-width: 18px;
        font-size: 10px;
        margin-left: auto;
        font-weight: bold;
        padding: 0 5px;
        position: relative;
        right: 10px;
    }

    /* Make sure the menu text doesn't wrap */
    .menu-text {
        flex: 1;
    }

    /* Make the menu link a flex container */
    .menu-link {
        display: flex !important;
        align-items: center;
    }
    </style>

    <div class="aside-menu-wrapper flex-column-fluid" id="kt_aside_menu_wrapper">

        <div id="kt_aside_menu" class="aside-menu" data-menu-vertical="1" data-menu-scroll="1"
            data-menu-dropdown-timeout="500">

            <ul class="menu-nav">

                <x-aside-item text="Dashboard" active="dashboard" route="dashboard" icon="fa fa-home" />

                <li class="menu-item menu-item-submenu
                    @if (strpos($current_route, 'scripts.') !== false || strpos(Route::currentRouteName(), 'excel') !== false) menu-item-open menu-item-here @endif
                    ">
                    <a href="javascript:;" class="menu-link menu-toggle">
                        <i class="menu-icon fa fa-cog"></i>
                        <span class="menu-text">Scripts</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="menu-submenu">
                        <i class="menu-arrow"></i>
                        <ul class="menu-subnav">
                            @if (Auth::user()->role === User::ROLE_ADMIN)
                            <x-aside-item text="Bulk Import" route="excel.staff-bulk-import" />
                            <x-aside-item-with-count text="All" route="scripts.all" :count="$allCount ?? 0" />
                            <x-aside-item-with-count text="Ready to Send" route="scripts.ready-to-send"
                               
                                    :count="$readyToSendCount ?? 0" />
                            <x-aside-item-with-count text="Sent" route="scripts.sent" :count="$sentCount" />
                            @elseif (Auth::user()->role === User::ROLE_PROVIDER)
                            <x-aside-item text="Add New" route="excel.new-import" />
                            <x-aside-item text="Bulk Import" route="excel.import" />
                            <x-aside-item-with-count text="Ready to Sign" route="scripts.ready-to-sign"
                                :count="$readyToSignCount" />
                            <x-aside-item-with-count text="Pending Approval" route="scripts.provider-pending-approval"
                                :count="$pendingApprovalCount" />
                            <x-aside-item-with-count text="Sent" route="scripts.sent" :count="$sentCount" />
                            @elseif (Auth::user()->role === User::ROLE_OPERATOR)
                            <x-aside-item text="Bulk Import" route="excel.staff-bulk-import" />
                            {{-- <x-aside-item text="All" route="scripts.all" /> --}}
                            <x-aside-item-with-count text="Ready to Send" route="scripts.ready-to-send"
                                   
                                :count="$readyToSendCount ?? 0" />
                            <x-aside-item-with-count text="Sent" route="scripts.sent" :count="$sentCount" />
                            @endif
                        </ul>
                    </div>
                </li>

                @if (Auth::user()->role === User::ROLE_PROVIDER)
                {{-- <x-aside-item text="Add New" active="imports" route="imports.index" icon="fa fa-flag" /> --}}
                {{-- <li class="menu-item @if (strpos(Route::currentRouteName(), 'imports') !== false) menu-item-active @endif">
                        <a href="{{ url('/excel-import') }}" class="menu-link">
                <i class="menu-icon fa fa-flag"><span></span></i>
                <span class="menu-text">Add New</span>
                </a>
                </li> --}}
                @endif

                @if (Auth::user()->role === User::ROLE_PROVIDER)
                @php
                // Count all files for archive
                $archiveCount = ImportFile::whereHas('import', function ($q) use ($user) {
                $q->where('user_id', $user->id);
                })->count();
                @endphp
                <x-aside-item text="Archive" active="archive" route="archive.index" icon="fa fa-globe-americas" />
                @endif

                @if (Auth::user()->role === User::ROLE_OPERATOR || Auth::user()->role === User::ROLE_ADMIN)
                {{-- @php
                        // Count providers
                        $providersCount = \App\Models\User::where('role', User::ROLE_PROVIDER)->count();
                    @endphp --}}
                <x-aside-item text="Providers" active="users" route="users.index" icon="fa fa-users" />
                @endif

                @if (Auth::user()->role === User::ROLE_ADMIN)
                {{-- @php
                        // Count staff (operators)
                        $staffCount = \App\Models\User::where('role', User::ROLE_OPERATOR)->count();
                    @endphp --}}
                <x-aside-item text="Staff" active="admin" route="admin.index" icon="fa fa-user" />
                @endif
                <!--<x-aside-item text="Universities" active="universities" route="dashboard" icon="fa fa-city" />
                <x-aside-item text="Services" active="services" route="dashboard" icon="fa fa-briefcase" />
                <x-aside-item text="Clients" active="clients" route="dashboard" icon="fa fa-users" />
                <x-aside-item text="Admins" active="admins" route="dashboard" icon="fa fa-users" />
                <x-aside-item text="Home Slider" active="home-slider" route="dashboard" icon="fa fa-images" /> -->

                <li class="menu-item menu-item-submenu
                    @if (strpos($current_route, 'settings.') !== false) menu-item-open menu-item-here @endif
                    ">
                    <a href="javascript:;" class="menu-link menu-toggle">
                        <i class="menu-icon fa fa-cog"></i>
                        <span class="menu-text">Settings</span>
                        <i class="menu-arrow"></i>
                    </a>
                    <div class="menu-submenu">
                        <i class="menu-arrow"></i>
                        <ul class="menu-subnav">

                            <x-aside-item text="Change Password" route="settings.change-password" />
                            @if (Auth::user()->role === User::ROLE_ADMIN)
                            <x-aside-item text="Fax Options" route="settings.fax-options" />
                                <x-aside-item text="Logs" route="settings.logs" />
                            @endif

                            <!-- <x-aside-item text="Terms & Conditions" route="settings.terms.index"/>
                            <x-aside-item text="Privacy Policy" route="settings.privacy.index"/>
                            <x-aside-item text="About Us" route="settings.about.index"/> -->

                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>