[2025-06-02 10:37:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:07:52 +0630","user_id":1} 
[2025-06-02 10:38:26] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"14527864785","label":"list"}]} 
[2025-06-02 10:40:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:10:11 +0630","user_id":1} 
[2025-06-02 10:40:33] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"14587458525","label":"test"}]} 
[2025-06-02 10:41:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:11:07 +0630","user_id":1} 
[2025-06-02 10:41:29] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"**********4","label":"new test"}]} 
[2025-06-02 10:42:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:12:54 +0630","user_id":1} 
[2025-06-02 10:43:11] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"25463125741","label":"qwerty"}]} 
[2025-06-02 10:43:27] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"},{"id":26,"numbers":"25463125741","label":"qwerty"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:45:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:15:10 +0630","user_id":1} 
[2025-06-02 10:47:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:17:23 +0630","user_id":1} 
[2025-06-02 10:47:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:17:37 +0630","user_id":1} 
[2025-06-02 10:47:41] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:48:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:18:10 +0630","user_id":1} 
[2025-06-02 10:49:06] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:19:05 +0630","user_id":1} 
[2025-06-02 10:49:54] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:50:31] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"13151546358","label":""}]} 
[2025-06-02 10:50:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:20:37 +0630","user_id":1} 
[2025-06-02 10:50:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:20:55 +0630","user_id":1} 
[2025-06-02 10:51:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:21:03 +0630","user_id":1} 
[2025-06-02 10:51:27] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"**********5","label":""}]} 
[2025-06-02 10:51:33] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:51:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:21:55 +0630","user_id":1} 
[2025-06-02 10:52:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:22:46 +0630","user_id":1} 
[2025-06-02 10:52:50] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:53:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:23:02 +0630","user_id":1} 
[2025-06-02 10:53:05] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:53:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:23:14 +0630","user_id":1} 
[2025-06-02 10:54:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:24:57 +0630","user_id":1} 
[2025-06-02 10:55:02] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:55:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:25:31 +0630","user_id":1} 
[2025-06-02 10:55:34] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:56:54] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:26:54 +0630","user_id":1} 
[2025-06-02 10:57:36] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:27:36 +0630","user_id":1} 
[2025-06-02 10:58:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:28:11 +0630","user_id":1} 
[2025-06-02 10:58:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:28:57 +0630","user_id":1} 
[2025-06-02 10:59:00] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 11:01:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:31:04 +0630","user_id":1} 
[2025-06-02 11:01:09] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 11:01:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:31:31 +0630","user_id":1} 
[2025-06-02 11:02:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:32:14 +0630","user_id":1} 
[2025-06-02 11:03:00] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:33:00 +0630","user_id":1} 
[2025-06-02 11:03:06] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 11:04:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:34:15 +0630","user_id":1} 
[2025-06-02 11:04:19] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 11:05:20] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:35:19 +0630","user_id":1} 
[2025-06-02 11:05:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:35:56 +0630","user_id":1} 
[2025-06-02 11:08:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:38:37 +0630","user_id":1} 
[2025-06-02 11:09:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:39:53 +0630","user_id":1} 
[2025-06-02 11:10:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:40:52 +0630","user_id":1} 
[2025-06-02 11:11:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:41:36 +0630","user_id":1} 
[2025-06-02 11:12:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:42:20 +0630","user_id":1} 
[2025-06-02 11:12:50] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:42:49 +0630","user_id":1} 
[2025-06-02 11:13:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:43:15 +0630","user_id":1} 
[2025-06-02 11:13:34] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"87451256352","label":"qwerty"}]} 
[2025-06-02 11:20:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:50:46 +0630","user_id":1} 
[2025-06-02 11:22:22] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:52:21 +0630","user_id":1} 
[2025-06-02 11:23:32] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"},{"id":27,"numbers":"87451256352","label":"qwerty"}],"newFaxInputs":[{"number":"74527684527","label":"sdfd"}]} 
[2025-06-02 11:27:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:57:55 +0630","user_id":1} 
[2025-06-02 11:28:46] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"87542785784","label":"new"}]} 
[2025-06-02 11:33:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:03:24 +0630","user_id":1} 
[2025-06-02 11:33:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:03:46 +0630","user_id":1} 
[2025-06-02 11:34:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:04:36 +0630","user_id":1} 
[2025-06-02 11:37:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:07:07 +0630","user_id":1} 
[2025-06-02 11:40:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:10:50 +0630","user_id":1} 
[2025-06-02 11:41:00] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:41:00] local.INFO: Duplicate found within form data  
[2025-06-02 11:41:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:11:34 +0630","user_id":1} 
[2025-06-02 11:42:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:12:06 +0630","user_id":1} 
[2025-06-02 11:42:15] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:42:15] local.INFO: Duplicate found within form data  
[2025-06-02 11:42:17] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:42:17] local.INFO: Duplicate found within form data  
[2025-06-02 11:43:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:13:22 +0630","user_id":1} 
[2025-06-02 11:43:29] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:43:29] local.INFO: Duplicate found within form data  
[2025-06-02 11:43:45] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:13:44 +0630","user_id":1} 
[2025-06-02 11:43:58] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:43:58] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:29] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:14:28 +0630","user_id":1} 
[2025-06-02 11:44:33] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:33] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:47] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:47] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:49] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:49] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:50] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:50] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:51] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:51] local.INFO: Duplicate found within form data  
[2025-06-02 11:45:03] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528"],"unique":["18885110528"],"count_all":1,"count_unique":1} 
[2025-06-02 11:45:03] local.INFO: Checking existing fax number against database: {"index":0,"number":"18885110528","id":16,"exists":false} 
[2025-06-02 11:45:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:15:28 +0630","user_id":1} 
[2025-06-02 11:46:38] local.ERROR: Undefined variable $message {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1897391552 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#586</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1897391552\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-1181198962 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">Fax Options</span>\"
</pre><script>Sfdump(\"sf-dump-1181198962\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $message at C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php:14)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#11 C:\\KodeCreators\\newlife-panel\\resources\\views\\settings\\fax-options.blade.php(3): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#15 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#74 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $message at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php:14)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 14)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php(14): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 14)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#13 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\4c5d02dddc567ffb1e82b8ff009dacb7.php(10): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#76 {main}
"} 
[2025-06-02 11:46:50] local.ERROR: syntax error, unexpected token "endif", expecting end of file {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#586</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-56366042 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">Fax Options</span>\"
</pre><script>Sfdump(\"sf-dump-56366042\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): syntax error, unexpected token \"endif\", expecting end of file at C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php:17)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected token \"endif\", expecting end of file at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php:18)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#1 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#10 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\4c5d02dddc567ffb1e82b8ff009dacb7.php(10): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#16 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#73 {main}
"} 
[2025-06-02 11:47:03] local.ERROR: Undefined variable $message {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1286605506 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#586</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1286605506\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-288875075 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">Fax Options</span>\"
</pre><script>Sfdump(\"sf-dump-288875075\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $message at C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php:14)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#11 C:\\KodeCreators\\newlife-panel\\resources\\views\\settings\\fax-options.blade.php(3): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#15 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#74 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $message at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php:14)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 14)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php(14): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 14)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#13 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\4c5d02dddc567ffb1e82b8ff009dacb7.php(10): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#76 {main}
"} 
[2025-06-02 11:47:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:17:30 +0630","user_id":1} 
[2025-06-02 11:58:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:28:11 +0630","user_id":1} 
[2025-06-02 11:58:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:28:29 +0630","user_id":1} 
[2025-06-02 11:58:32] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 11:58:34] local.INFO: Staff bulk import completed {"import_id":117,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 11:58:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:28:37 +0630","user_id":1} 
[2025-06-02 11:59:27] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:29:26 +0630","user_id":1} 
[2025-06-02 11:59:59] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:29:58 +0630","user_id":1} 
[2025-06-02 12:00:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:30:02 +0630","user_id":1} 
[2025-06-02 12:00:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:30:38 +0630","user_id":1} 
[2025-06-02 12:01:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:31:04 +0630","user_id":3} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":2,"consecutive_blank_rows":1} 
[2025-06-02 12:01:09] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":1} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":3,"consecutive_blank_rows":2} 
[2025-06-02 12:01:09] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":2} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":4,"consecutive_blank_rows":3} 
[2025-06-02 12:01:09] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":3} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":5,"consecutive_blank_rows":4} 
[2025-06-02 12:01:09] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":4} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-02 12:01:09] local.INFO: 5 or more consecutive blank rows detected - stopping processing and discarding blank rows {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-02 12:01:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:31:10 +0630","user_id":3} 
[2025-06-02 12:01:42] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:31:41 +0630","user_id":1} 
[2025-06-02 12:02:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:32:02 +0630","user_id":1} 
[2025-06-02 12:02:40] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 12:02:41] local.INFO: Staff bulk import completed {"import_id":118,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 12:02:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:32:43 +0630","user_id":1} 
[2025-06-02 12:03:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:03 +0630","user_id":1} 
[2025-06-02 12:03:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:06 +0630","user_id":1} 
[2025-06-02 12:03:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:10 +0630","user_id":1} 
[2025-06-02 12:03:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:15 +0630","user_id":1} 
[2025-06-02 12:03:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:55 +0630","user_id":1} 
[2025-06-02 12:04:14] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 12:04:15] local.INFO: Staff bulk import completed {"import_id":119,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 12:04:18] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:34:17 +0630","user_id":1} 
[2025-06-02 12:04:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:34:34 +0630","user_id":3} 
[2025-06-02 12:04:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:34:57 +0630","user_id":1} 
[2025-06-02 12:05:16] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png","exists":false,"full_path":"C:\\KodeCreators\\newlife-panel\\storage\\app/public/signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png"} 
[2025-06-02 12:05:19] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png","exists":false,"full_path":"C:\\KodeCreators\\newlife-panel\\storage\\app/public/signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png"} 
[2025-06-02 12:05:22] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:35:21 +0630","user_id":1} 
[2025-06-02 12:05:26] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:35:25 +0630","user_id":1} 
[2025-06-02 12:06:14] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png","exists":false,"full_path":"C:\\KodeCreators\\newlife-panel\\storage\\app/public/signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png"} 
[2025-06-02 12:06:17] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:36:16 +0630","user_id":1} 
[2025-06-02 12:06:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:36:20 +0630","user_id":1} 
[2025-06-02 12:06:23] local.INFO: Filtering by provider {"provider_id":"3"} 
[2025-06-02 12:06:27] local.INFO: Filtering by provider {"provider_id":"4"} 
[2025-06-02 12:06:42] local.INFO: Downloaded PDFs {"total_files":9,"signed_and_sent_for_approval_files":0,"added_to_zip":9} 
[2025-06-02 12:06:58] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:36:57 +0630","error":"Trailing data"} 
[2025-06-02 12:06:58] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:34:34 +0630"} 
[2025-06-02 12:06:58] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":727,"signed_at_db":"2025-06-02 17:34:34","formatted_signed_at":"06/02/2025 05:34 PM"} 
[2025-06-02 12:06:59] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:07:02] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:01 +0630","user_id":3} 
[2025-06-02 12:07:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:04 +0630","user_id":3} 
[2025-06-02 12:07:17] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:16 +0630","user_id":1} 
[2025-06-02 12:07:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:31 +0630","user_id":1} 
[2025-06-02 12:07:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:34 +0630","user_id":1} 
[2025-06-02 12:07:49] local.INFO: Fax sent successfully {"to":["+18885110528"],"from":"+18669938841","file":"/storage/transient-6c8bafa7b84c43c99e0cc9e77013e536.pdf"} 
[2025-06-02 12:07:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:51 +0630","user_id":1} 
[2025-06-02 12:07:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:54 +0630","user_id":1} 
[2025-06-02 12:08:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:15 +0630","user_id":3} 
[2025-06-02 12:08:21] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:38:20 +0630","error":"Trailing data"} 
[2025-06-02 12:08:21] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:38:15 +0630"} 
[2025-06-02 12:08:21] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":728,"signed_at_db":"2025-06-02 17:38:15","formatted_signed_at":"06/02/2025 05:38 PM"} 
[2025-06-02 12:08:21] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:08:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:23 +0630","user_id":3} 
[2025-06-02 12:08:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:31 +0630","user_id":3} 
[2025-06-02 12:08:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:48 +0630","user_id":3} 
[2025-06-02 12:08:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:54 +0630","user_id":3} 
[2025-06-02 12:09:05] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:39:04 +0630","error":"Trailing data"} 
[2025-06-02 12:09:05] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:38:54 +0630"} 
[2025-06-02 12:09:05] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":729,"signed_at_db":"2025-06-02 17:38:54","formatted_signed_at":"06/02/2025 05:38 PM"} 
[2025-06-02 12:09:05] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:09:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:07 +0630","user_id":3} 
[2025-06-02 12:09:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:10 +0630","user_id":3} 
[2025-06-02 12:09:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:28 +0630","user_id":3} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-02 12:09:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:34 +0630","user_id":3} 
[2025-06-02 12:09:36] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-02 12:09:36] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":120} 
[2025-06-02 12:09:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:38 +0630","user_id":3} 
[2025-06-02 12:09:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:42 +0630","user_id":3} 
[2025-06-02 12:09:46] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:39:45 +0630","error":"Trailing data"} 
[2025-06-02 12:09:46] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:39:42 +0630"} 
[2025-06-02 12:09:46] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":736,"signed_at_db":"2025-06-02 17:39:42","formatted_signed_at":"06/02/2025 05:39 PM"} 
[2025-06-02 12:09:46] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:09:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:48 +0630","user_id":3} 
[2025-06-02 12:09:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:50 +0630","user_id":3} 
[2025-06-02 12:09:59] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:58 +0630","user_id":3} 
[2025-06-02 12:10:01] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:01 +0630","user_id":3} 
[2025-06-02 12:10:01] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:40:01 +0630","error":"Trailing data"} 
[2025-06-02 12:10:01] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:40:01 +0630"} 
[2025-06-02 12:10:01] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":737,"signed_at_db":"2025-06-02 17:40:01","formatted_signed_at":"06/02/2025 05:40 PM"} 
[2025-06-02 12:10:02] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:10:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:04 +0630","user_id":3} 
[2025-06-02 12:10:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:07 +0630","user_id":3} 
[2025-06-02 12:10:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:31 +0630","user_id":1} 
[2025-06-02 12:10:35] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png","exists":false,"full_path":"C:\\KodeCreators\\newlife-panel\\storage\\app/public/signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png"} 
[2025-06-02 12:10:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:38 +0630","user_id":1} 
[2025-06-02 12:10:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:43 +0630","user_id":1} 
[2025-06-02 12:13:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:43:55 +0630","user_id":1} 
[2025-06-02 12:14:45] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:44:44 +0630","user_id":1} 
[2025-06-02 12:14:57] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:44:56 +0630","user_id":1} 
[2025-06-02 12:16:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:46:12 +0630","user_id":1} 
[2025-06-02 12:19:29] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:49:28 +0630","user_id":1} 
[2025-06-02 12:19:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:49:54 +0630","user_id":1} 
[2025-06-02 12:19:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:49:57 +0630","user_id":1} 
[2025-06-02 12:20:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:50:46 +0630","user_id":1} 
[2025-06-02 12:22:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:52:29 +0630","user_id":1} 
[2025-06-02 12:23:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:10 +0630","user_id":3} 
[2025-06-02 12:23:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:14 +0630","user_id":3} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-02 12:23:20] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:19 +0630","user_id":3} 
[2025-06-02 12:23:21] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-02 12:23:22] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":1} 
[2025-06-02 12:23:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:23 +0630","user_id":3} 
[2025-06-02 12:23:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:31 +0630","user_id":3} 
[2025-06-02 12:23:33] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:53:33 +0630","error":"Trailing data"} 
[2025-06-02 12:23:33] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:53:31 +0630"} 
[2025-06-02 12:23:33] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":5,"signed_at_db":"2025-06-02 17:53:31","formatted_signed_at":"06/02/2025 05:53 PM"} 
[2025-06-02 12:23:34] local.ERROR: The PHP GD extension is required, but is not installed. {"userId":3,"exception":"[object] (Exception(code: 0): The PHP GD extension is required, but is not installed. at C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\lib\\Cpdf.php:5813)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Adapter\\CPDF.php(669): Dompdf\\Cpdf->addPngFromFile('file://C:\\\\KodeC...', 42.265748031496, 280.9332519685, 82.727272727273, 60.0)
#1 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer\\Image.php(65): Dompdf\\Adapter\\CPDF->image('file://C:\\\\KodeC...', 42.265748031496, 451.0667480315, 82.727272727273, 60.0, 'normal')
#2 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(289): Dompdf\\Renderer\\Image->render(Object(Dompdf\\FrameDecorator\\Image))
#3 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(128): Dompdf\\Renderer->_render_frame('image', Object(Dompdf\\FrameDecorator\\Image))
#4 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(195): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Image))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(195): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Block))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(195): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Block))
#7 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(195): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Block))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\FrameReflower\\Page.php(149): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Block))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\FrameDecorator\\AbstractFrameDecorator.php(913): Dompdf\\FrameReflower\\Page->reflow(NULL)
#10 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Dompdf.php(765): Dompdf\\FrameDecorator\\AbstractFrameDecorator->reflow()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\barryvdh\\laravel-dompdf\\src\\PDF.php(237): Dompdf\\Dompdf->render()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\barryvdh\\laravel-dompdf\\src\\PDF.php(186): Barryvdh\\DomPDF\\PDF->render()
#13 C:\\KodeCreators\\newlife-panel\\app\\Http\\Controllers\\Admin\\ScriptController.php(1429): Barryvdh\\DomPDF\\PDF->output()
#14 C:\\KodeCreators\\newlife-panel\\app\\Http\\Controllers\\Admin\\ScriptController.php(1174): App\\Http\\Controllers\\Admin\\ScriptController->regeneratePdfWithSignature(Object(App\\Models\\ImportFile))
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\ScriptController->signAll(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('signAll', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\ScriptController), 'signAll')
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#68 {main}
"} 
[2025-06-02 12:23:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:43 +0630","user_id":3} 
[2025-06-02 12:24:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:54:30 +0630","user_id":3} 
[2025-06-02 12:24:50] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:54:49 +0630","user_id":3} 
[2025-06-02 12:24:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:54:55 +0630","user_id":3} 
[2025-06-02 12:24:59] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:54:59 +0630","user_id":3} 
[2025-06-02 12:25:01] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:55:01 +0630","error":"Trailing data"} 
[2025-06-02 12:25:01] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:54:59 +0630"} 
[2025-06-02 12:25:01] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":6,"signed_at_db":"2025-06-02 17:54:59","formatted_signed_at":"06/02/2025 05:54 PM"} 
[2025-06-02 12:25:02] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:25:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:55:04 +0630","user_id":3} 
[2025-06-02 12:25:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:55:08 +0630","user_id":3} 
[2025-06-02 12:25:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:55:29 +0630","user_id":1} 
[2025-06-02 12:31:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 18:01:55 +0630","user_id":3} 
[2025-06-02 12:32:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 18:02:05 +0630","user_id":1} 
[2025-06-03 04:16:54] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:46:53 +0630","user_id":1} 
[2025-06-03 04:16:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:46:56 +0630","user_id":1} 
[2025-06-03 04:17:04] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:02 +0630","user_id":1} 
[2025-06-03 04:17:10] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:09 +0630","user_id":1} 
[2025-06-03 04:17:13] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:12 +0630","user_id":1} 
[2025-06-03 04:17:16] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:15 +0630","user_id":1} 
[2025-06-03 04:17:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:29 +0630","user_id":1} 
[2025-06-03 04:17:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:37 +0630","user_id":1} 
[2025-06-03 04:17:49] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:48 +0630","user_id":1} 
[2025-06-03 04:17:51] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:51 +0630","user_id":1} 
[2025-06-03 04:19:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:49:17 +0630","user_id":1} 
[2025-06-03 04:20:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:50:30 +0630","user_id":1} 
[2025-06-03 04:21:17] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:51:16 +0630","user_id":1} 
[2025-06-03 04:21:56] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:51:55 +0630","user_id":1} 
[2025-06-03 04:22:28] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 04:22:31] local.INFO: Staff bulk import completed {"import_id":2,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 04:22:34] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:52:33 +0630","user_id":1} 
[2025-06-03 04:23:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:53:11 +0630","user_id":3} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-03 04:23:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:53:20 +0630","user_id":3} 
[2025-06-03 04:23:22] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-03 04:23:22] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":3} 
[2025-06-03 04:23:25] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:53:24 +0630","user_id":3} 
[2025-06-03 04:23:59] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:53:58 +0630","user_id":1} 
[2025-06-03 04:24:21] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:54:21 +0630","user_id":1} 
[2025-06-03 04:24:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:54:31 +0630","user_id":1} 
[2025-06-03 04:25:19] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:55:18 +0630","user_id":1} 
[2025-06-03 04:26:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:56:52 +0630","user_id":1} 
[2025-06-03 04:31:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:01:48 +0630","user_id":1} 
[2025-06-03 04:31:59] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:01:59 +0630","user_id":1} 
[2025-06-03 04:33:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:03:31 +0630","user_id":1} 
[2025-06-03 04:33:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:03:41 +0630","user_id":1} 
[2025-06-03 04:33:51] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:03:50 +0630","user_id":1} 
[2025-06-03 04:33:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:03:56 +0630","user_id":1} 
[2025-06-03 04:34:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:04:04 +0630","user_id":1} 
[2025-06-03 04:34:07] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 04:34:08] local.INFO: Staff bulk import completed {"import_id":4,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 04:34:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:04:10 +0630","user_id":1} 
[2025-06-03 04:46:52] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:16:52 +0630","user_id":1} 
[2025-06-03 04:48:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:18:41 +0630","user_id":1} 
[2025-06-03 04:48:50] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:18:50 +0630","user_id":1} 
[2025-06-03 04:48:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:18:54 +0630","user_id":1} 
[2025-06-03 04:49:00] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:19:00 +0630","user_id":1} 
[2025-06-03 04:50:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:20:26 +0630","user_id":1} 
[2025-06-03 04:50:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:20:30 +0630","user_id":1} 
[2025-06-03 04:50:35] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:20:34 +0630","user_id":1} 
[2025-06-03 04:51:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:21:37 +0630","user_id":1} 
[2025-06-03 04:51:44] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:21:44 +0630","user_id":1} 
[2025-06-03 04:51:54] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:21:53 +0630","user_id":1} 
[2025-06-03 04:52:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:22:30 +0630","user_id":1} 
[2025-06-03 04:52:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:22:43 +0630","user_id":1} 
[2025-06-03 04:57:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:27:10 +0630","user_id":1} 
[2025-06-03 04:57:58] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:27:57 +0630","user_id":1} 
[2025-06-03 04:58:41] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:28:40 +0630","user_id":1} 
[2025-06-03 04:59:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:29:54 +0630","user_id":1} 
[2025-06-03 05:00:13] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:30:13 +0630","user_id":1} 
[2025-06-03 05:01:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:31:05 +0630","user_id":1} 
[2025-06-03 05:01:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:31:30 +0630","user_id":1} 
[2025-06-03 05:01:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:31:48 +0630","user_id":1} 
[2025-06-03 05:02:49] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:32:48 +0630","user_id":1} 
[2025-06-03 05:03:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:33:17 +0630","user_id":1} 
[2025-06-03 05:17:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:47:25 +0630","user_id":1} 
[2025-06-03 05:21:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:51:31 +0630","user_id":1} 
[2025-06-03 05:21:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:51:52 +0630","user_id":1} 
[2025-06-03 05:22:01] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:52:00 +0630","user_id":1} 
[2025-06-03 05:23:39] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:53:39 +0630","user_id":1} 
[2025-06-03 05:24:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:54:43 +0630","user_id":1} 
[2025-06-03 05:25:41] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:55:41 +0630","user_id":1} 
[2025-06-03 05:27:40] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:57:39 +0630","user_id":1} 
[2025-06-03 05:28:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:58:31 +0630","user_id":1} 
[2025-06-03 05:28:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:58:56 +0630","user_id":1} 
[2025-06-03 05:29:25] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:59:25 +0630","user_id":1} 
[2025-06-03 05:30:08] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:07 +0630","user_id":1} 
[2025-06-03 05:30:13] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:13 +0630","user_id":1} 
[2025-06-03 05:30:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:31 +0630","user_id":4} 
[2025-06-03 05:30:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:41 +0630","user_id":1} 
[2025-06-03 05:30:50] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"4","selected_provider_name":"ashish parmar"} 
[2025-06-03 05:30:50] local.INFO: Staff bulk import completed {"import_id":5,"processed_rows":9,"skipped_rows":0,"provider_id":"4","provider_name":"aashish"} 
[2025-06-03 05:30:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:53 +0630","user_id":1} 
[2025-06-03 05:31:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:01:29 +0630","user_id":1} 
[2025-06-03 05:32:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:02:48 +0630","user_id":1} 
[2025-06-03 05:33:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:03:31 +0630","user_id":1} 
[2025-06-03 05:34:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:04:17 +0630","user_id":1} 
[2025-06-03 05:34:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:04:48 +0630","user_id":1} 
[2025-06-03 05:36:41] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:06:40 +0630","user_id":1} 
[2025-06-03 05:36:50] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:06:49 +0630","user_id":1} 
[2025-06-03 05:37:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:01 +0630","user_id":1} 
[2025-06-03 05:37:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:22 +0630","user_id":1} 
[2025-06-03 05:37:28] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:28 +0630","user_id":1} 
[2025-06-03 05:37:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:37 +0630","user_id":1} 
[2025-06-03 05:37:54] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:53 +0630","user_id":1} 
[2025-06-03 05:39:03] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:09:02 +0630","user_id":1} 
[2025-06-03 05:39:04] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"4","selected_provider_name":"ashish parmar"} 
[2025-06-03 05:39:05] local.INFO: Staff bulk import completed {"import_id":6,"processed_rows":9,"skipped_rows":0,"provider_id":"4","provider_name":"aashish"} 
[2025-06-03 05:39:08] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:09:07 +0630","user_id":1} 
[2025-06-03 05:41:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:19 +0630","user_id":1} 
[2025-06-03 05:41:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:37 +0630","user_id":4} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-03 05:41:44] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:44 +0630","user_id":4} 
[2025-06-03 05:41:45] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-03 05:41:46] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":7} 
[2025-06-03 05:41:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:47 +0630","user_id":4} 
[2025-06-03 05:41:51] local.INFO: Update Status Request {"import_id":"7","ids":null,"status":"Pending Approval"} 
[2025-06-03 05:41:51] local.INFO: Processing all records for import {"import_id":"7"} 
[2025-06-03 05:41:51] local.INFO: Filtering for New status only  
[2025-06-03 05:41:51] local.INFO: Update Status Query {"sql":"select * from `import_files` where `import_id` = ? and `status` = ?","bindings":["7","New"]} 
[2025-06-03 05:41:51] local.INFO: Records that will be updated {"record_ids":[55,56,57,58,59,60,61,62,63],"count":9} 
[2025-06-03 05:41:51] local.INFO: User information for signature {"user_id":4,"user_name":"ashish parmar","has_signature":"Yes","signature_path":"signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png"} 
[2025-06-03 05:41:51] local.INFO: Signature image loaded successfully {"user_id":4,"signature_path":"signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png","mime_type":"image/png"} 
[2025-06-03 05:41:51] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-03 11:11:47 +0630"} 
[2025-06-03 05:41:51] local.INFO: Starting PDF regeneration for 9 files  
[2025-06-03 05:41:52] local.INFO: Processed 5 of 9 PDFs  
[2025-06-03 05:41:53] local.INFO: Completed PDF regeneration. Processed 9 of 9 files  
[2025-06-03 05:41:54] local.INFO: ScriptStatusChanged event dispatched {"user_id":4,"user_name":"ashish parmar","count":9} 
[2025-06-03 05:41:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:56 +0630","user_id":4} 
[2025-06-03 05:44:37] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:14:36 +0630","user_id":1} 
[2025-06-03 05:45:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:15:37 +0630","user_id":1} 
[2025-06-03 05:47:08] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:17:07 +0630","user_id":1} 
[2025-06-03 05:48:35] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:18:34 +0630","user_id":1} 
[2025-06-03 05:49:45] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:19:45 +0630","user_id":1} 
[2025-06-03 05:50:07] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:20:07 +0630","user_id":1} 
[2025-06-03 05:50:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:20:29 +0630","user_id":1} 
[2025-06-03 05:50:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:20:41 +0630","user_id":1} 
[2025-06-03 05:55:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:25:21 +0630","user_id":1} 
[2025-06-03 05:55:36] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:25:35 +0630","user_id":1} 
[2025-06-03 05:57:04] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:27:03 +0630","user_id":1} 
[2025-06-03 05:57:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:27:19 +0630","user_id":1} 
[2025-06-03 06:05:15] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:35:14 +0630","user_id":1} 
[2025-06-03 06:05:28] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:35:28 +0630","user_id":1} 
[2025-06-03 06:06:50] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:36:50 +0630","user_id":1} 
[2025-06-03 06:07:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:37:21 +0630","user_id":1} 
[2025-06-03 06:09:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:39:42 +0630","user_id":1} 
[2025-06-03 06:10:07] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:40:06 +0630","user_id":1} 
[2025-06-03 06:10:12] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:40:11 +0630","user_id":1} 
[2025-06-03 06:10:29] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:40:28 +0630","user_id":1} 
[2025-06-03 06:16:13] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:46:12 +0630","user_id":1} 
[2025-06-03 06:16:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:46:21 +0630","user_id":1} 
[2025-06-03 06:16:34] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"4","selected_provider_name":"ashish parmar"} 
[2025-06-03 06:16:34] local.INFO: Staff bulk import completed {"import_id":8,"processed_rows":9,"skipped_rows":0,"provider_id":"4","provider_name":"aashish"} 
[2025-06-03 06:16:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:46:37 +0630","user_id":1} 
[2025-06-03 06:16:50] local.INFO: Download All PDF Request {"displayed_ids":["64","65","66","67","68","69","70","71","72"],"all_with_status":null,"import_id":"8","status":"New","route":"excel.download-all-pdf","request_method":"POST","all_request_params":{"_token":"1oqD7zE5N5PUR9Lh1EbyftaQ0fObjzqjLjCfnGi1","status":"created","displayed_ids":["64","65","66","67","68","69","70","71","72"]}} 
[2025-06-03 06:16:50] local.INFO: Initial query before filtering by IDs {"sql":"select * from `import_files` where `import_id` = ? and (`status` = ? or `status` is null)","bindings":[8,"New"]} 
[2025-06-03 06:16:50] local.INFO: Record count before ID filtering {"count":9} 
[2025-06-03 06:16:50] local.INFO: Filtering by displayed IDs {"count":9,"ids":[64,65,66,67,68,69,70,71,72]} 
[2025-06-03 06:16:50] local.INFO: Query after filtering by IDs {"sql":"select * from `import_files` where `import_id` = ? and (`status` = ? or `status` is null) and `id` in (?, ?, ?, ?, ?, ?, ?, ?, ?)","bindings":[8,"New",64,65,66,67,68,69,70,71,72]} 
[2025-06-03 06:16:50] local.INFO: Files that will be included in the ZIP {"count":9,"file_ids":[72,71,70,69,68,67,66,65,64],"file_names":["prescription_9_Lorraine_Geissler.pdf","prescription_8_Martin_Schoendienst.pdf","prescription_7_Teri_Degiacomo.pdf","prescription_6_Douglas_Forsman.pdf","prescription_5_Laura_Reynolds.pdf","prescription_4_Susan_Johnson.pdf","prescription_3_Brian_House.pdf","prescription_2_Brenda_Kirkpatrick.pdf","prescription_1_Lucia_Castillo_Dorantes.pdf"]} 
[2025-06-03 06:21:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:51:30 +0630","user_id":1} 
[2025-06-03 06:21:46] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:51:45 +0630","user_id":1} 
[2025-06-03 06:22:24] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:52:24 +0630","user_id":1} 
[2025-06-03 06:23:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:53:22 +0630","user_id":1} 
[2025-06-03 06:24:33] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:54:32 +0630","user_id":1} 
[2025-06-03 06:25:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:55:25 +0630","user_id":1} 
[2025-06-03 06:25:35] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:55:34 +0630","user_id":1} 
[2025-06-03 06:26:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:56:19 +0630","user_id":1} 
[2025-06-03 06:26:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:56:47 +0630","user_id":1} 
[2025-06-03 06:27:10] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:57:09 +0630","user_id":1} 
[2025-06-03 06:27:24] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:57:24 +0630","user_id":1} 
[2025-06-03 06:28:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:58:01 +0630","user_id":1} 
[2025-06-03 06:29:03] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:03 +0630","user_id":1} 
[2025-06-03 06:29:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:11 +0630","user_id":1} 
[2025-06-03 06:29:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:19 +0630","user_id":1} 
[2025-06-03 06:29:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:47 +0630","user_id":1} 
[2025-06-03 06:29:56] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:55 +0630","user_id":1} 
[2025-06-03 06:30:07] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:00:06 +0630","user_id":1} 
[2025-06-03 06:30:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:00:18 +0630","user_id":1} 
[2025-06-03 06:30:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:00:52 +0630","user_id":1} 
[2025-06-03 06:31:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:01:05 +0630","user_id":1} 
[2025-06-03 06:31:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:01:22 +0630","user_id":1} 
[2025-06-03 06:32:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:02:25 +0630","user_id":1} 
[2025-06-03 06:36:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:06:29 +0630","user_id":1} 
[2025-06-03 06:38:10] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:08:09 +0630","user_id":1} 
[2025-06-03 06:38:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:08:17 +0630","user_id":1} 
[2025-06-03 06:38:36] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:08:35 +0630","user_id":1} 
[2025-06-03 06:39:14] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:09:13 +0630","user_id":1} 
[2025-06-03 06:39:24] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:09:23 +0630","user_id":1} 
[2025-06-03 06:39:36] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:09:35 +0630","user_id":1} 
[2025-06-03 06:39:38] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 06:39:39] local.INFO: Staff bulk import completed {"import_id":9,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 06:39:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:09:41 +0630","user_id":1} 
[2025-06-03 06:44:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:14:30 +0630","user_id":1} 
[2025-06-03 06:44:36] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:14:35 +0630","user_id":1} 
[2025-06-03 06:44:50] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:14:49 +0630","user_id":1} 
[2025-06-03 06:44:54] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:14:54 +0630","user_id":1} 
[2025-06-03 06:49:17] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:19:17 +0630","user_id":1} 
[2025-06-03 06:49:27] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:19:27 +0630","user_id":1} 
[2025-06-03 06:51:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:21:19 +0630","user_id":1} 
[2025-06-03 06:53:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:23:20 +0630","user_id":1} 
[2025-06-03 06:53:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:23:29 +0630","user_id":1} 
[2025-06-03 07:02:19] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:32:19 +0630","user_id":1} 
[2025-06-03 07:02:28] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:32:28 +0630","user_id":1} 
[2025-06-03 07:04:14] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:34:14 +0630","user_id":1} 
[2025-06-03 07:04:24] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:34:23 +0630","user_id":1} 
[2025-06-03 07:04:28] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:34:27 +0630","user_id":1} 
[2025-06-03 07:04:47] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:34:46 +0630","user_id":1} 
[2025-06-03 07:05:11] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[5,6,7],"error_rows_count":3,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 07:05:11] local.INFO: Skipping row with errors {"excel_row_number":5,"index":3} 
[2025-06-03 07:05:11] local.INFO: Skipping row with errors {"excel_row_number":6,"index":4} 
[2025-06-03 07:05:11] local.INFO: Skipping row with errors {"excel_row_number":7,"index":5} 
[2025-06-03 07:05:11] local.INFO: Staff bulk import completed {"import_id":10,"processed_rows":6,"skipped_rows":3,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 07:05:14] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:35:14 +0630","user_id":1} 
[2025-06-03 07:05:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:35:25 +0630","user_id":1} 
[2025-06-03 07:05:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:35:38 +0630","user_id":1} 
[2025-06-03 07:09:06] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:39:05 +0630","user_id":4} 
[2025-06-03 07:09:16] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:39:15 +0630","user_id":1} 
[2025-06-03 07:09:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:39:32 +0630","user_id":1} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Error statistics {"total_rows":9,"error_rows":3,"valid_rows":6} 
[2025-06-03 07:10:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:40:20 +0630","user_id":4} 
[2025-06-03 07:10:50] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[5,6,7],"error_rows_count":3,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 07:10:51] local.INFO: Skipping row with errors {"excel_row_number":5,"index":3} 
[2025-06-03 07:10:51] local.INFO: Skipping row with errors {"excel_row_number":6,"index":4} 
[2025-06-03 07:10:51] local.INFO: Skipping row with errors {"excel_row_number":7,"index":5} 
[2025-06-03 07:10:51] local.INFO: Staff bulk import completed {"import_id":11,"processed_rows":6,"skipped_rows":3,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 07:10:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:40:53 +0630","user_id":1} 
[2025-06-03 07:10:59] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[5,6,7],"error_rows_count":3} 
[2025-06-03 07:10:59] local.INFO: Filtering out error rows before processing  
[2025-06-03 07:10:59] local.INFO: Skipping row with errors {"excel_row_number":5,"index":3} 
[2025-06-03 07:10:59] local.INFO: Skipping row with errors {"excel_row_number":6,"index":4} 
[2025-06-03 07:10:59] local.INFO: Skipping row with errors {"excel_row_number":7,"index":5} 
[2025-06-03 07:10:59] local.INFO: Excel processing completed {"processed_rows":6,"skipped_rows":3,"import_id":12} 
[2025-06-03 07:11:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:41:01 +0630","user_id":4} 
[2025-06-03 07:17:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:02 +0630","user_id":4} 
[2025-06-03 07:17:10] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:09 +0630","user_id":1} 
[2025-06-03 07:17:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:21 +0630","user_id":1} 
[2025-06-03 07:17:24] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[5,6,7],"error_rows_count":3,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 07:17:24] local.INFO: Skipping row with errors {"excel_row_number":5,"index":3} 
[2025-06-03 07:17:24] local.INFO: Skipping row with errors {"excel_row_number":6,"index":4} 
[2025-06-03 07:17:24] local.INFO: Skipping row with errors {"excel_row_number":7,"index":5} 
[2025-06-03 07:17:24] local.INFO: Staff bulk import completed {"import_id":13,"processed_rows":6,"skipped_rows":3,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 07:17:27] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:26 +0630","user_id":1} 
[2025-06-03 07:17:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:55 +0630","user_id":1} 
[2025-06-03 07:18:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:48:04 +0630","user_id":1} 
[2025-06-03 07:18:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:48:23 +0630","user_id":1} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":2,"consecutive_blank_rows":1} 
[2025-06-03 07:18:29] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":1} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":3,"consecutive_blank_rows":2} 
[2025-06-03 07:18:29] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":2} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":4,"consecutive_blank_rows":3} 
[2025-06-03 07:18:29] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":3} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":5,"consecutive_blank_rows":4} 
[2025-06-03 07:18:29] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":4} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-03 07:18:29] local.INFO: 5 or more consecutive blank rows detected - stopping processing and discarding blank rows {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-03 07:18:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:48:31 +0630","user_id":4} 
[2025-06-03 07:21:33] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:51:32 +0630","user_id":1} 
[2025-06-03 07:21:45] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:51:45 +0630","user_id":1} 
[2025-06-03 07:22:01] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:52:00 +0630","user_id":1} 
[2025-06-03 07:22:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:52:23 +0630","user_id":1} 
[2025-06-03 07:24:08] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:54:07 +0630","user_id":1} 
[2025-06-03 07:24:17] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:54:16 +0630","user_id":1} 
[2025-06-03 07:24:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:54:32 +0630","user_id":1} 
[2025-06-03 07:29:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:59:41 +0630","user_id":1} 
[2025-06-03 07:29:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:59:47 +0630","user_id":1} 
[2025-06-03 07:30:04] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:00:04 +0630","user_id":1} 
[2025-06-03 07:31:42] local.INFO: Processing Staff Excel data {"total_rows":25,"error_rows":[11,12,14,15,17,18,19,21,22,23,24,25],"error_rows_count":12,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":11,"index":9} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":12,"index":10} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":14,"index":12} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":15,"index":13} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":17,"index":15} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":18,"index":16} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":19,"index":17} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":21,"index":19} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":22,"index":20} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":23,"index":21} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":24,"index":22} 
[2025-06-03 07:31:43] local.INFO: Skipping empty row {"excel_row_number":25,"index":23} 
[2025-06-03 07:31:43] local.INFO: Staff bulk import completed {"import_id":14,"processed_rows":13,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 07:31:46] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:01:45 +0630","user_id":1} 
[2025-06-03 07:32:19] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:02:19 +0630","user_id":1} 
[2025-06-03 07:32:33] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:02:32 +0630","user_id":1} 
[2025-06-03 07:34:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:04:54 +0630","user_id":1} 
[2025-06-03 07:35:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:05:04 +0630","user_id":1} 
[2025-06-03 07:35:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:05:43 +0630","user_id":1} 
[2025-06-03 07:35:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:05:53 +0630","user_id":1} 
[2025-06-03 07:36:15] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:06:14 +0630","user_id":4} 
[2025-06-03 07:36:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:06:22 +0630","user_id":1} 
[2025-06-03 07:36:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:06:30 +0630","user_id":1} 
[2025-06-03 07:36:46] local.INFO: Blank row detected {"row_number":2,"consecutive_blank_rows":1} 
[2025-06-03 07:36:46] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":1} 
[2025-06-03 07:36:46] local.INFO: Blank row detected {"row_number":3,"consecutive_blank_rows":2} 
[2025-06-03 07:36:46] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":2} 
[2025-06-03 07:36:46] local.INFO: Blank row detected {"row_number":4,"consecutive_blank_rows":3} 
[2025-06-03 07:36:46] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":3} 
[2025-06-03 07:36:46] local.INFO: Blank row detected {"row_number":5,"consecutive_blank_rows":4} 
[2025-06-03 07:36:46] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":4} 
[2025-06-03 07:36:46] local.INFO: Blank row detected {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-03 07:36:46] local.INFO: 5 or more consecutive blank rows detected - stopping processing and discarding blank rows {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-03 07:36:49] local.INFO: Device time stored in session {"device_time":"2025-06-03 13:06:48 +0630","user_id":4} 
[2025-06-03 08:38:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:08:55 +0630","user_id":1} 
[2025-06-03 08:43:00] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:12:59 +0630","user_id":1} 
[2025-06-03 08:43:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:13:53 +0630","user_id":1} 
[2025-06-03 08:45:29] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:15:28 +0630","user_id":1} 
[2025-06-03 08:45:47] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:15:46 +0630","user_id":1} 
[2025-06-03 08:46:01] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:16:00 +0630","user_id":4} 
[2025-06-03 08:46:03] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:16:02 +0630","user_id":4} 
[2025-06-03 08:48:39] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:18:38 +0630","user_id":1} 
[2025-06-03 08:55:06] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:25:05 +0630","user_id":1} 
[2025-06-03 08:55:09] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:25:08 +0630","user_id":1} 
[2025-06-03 08:56:37] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:26:36 +0630","user_id":1} 
[2025-06-03 08:57:40] local.INFO: Processing Staff Excel data {"total_rows":25,"error_rows":[11,12,14,15,17,18,19,20,21,22,23,24,25],"error_rows_count":13,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":11,"index":9} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":12,"index":10} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":14,"index":12} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":15,"index":13} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":17,"index":15} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":18,"index":16} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":19,"index":17} 
[2025-06-03 08:57:41] local.INFO: Skipping row with errors {"excel_row_number":20,"index":18} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":21,"index":19} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":22,"index":20} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":23,"index":21} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":24,"index":22} 
[2025-06-03 08:57:41] local.INFO: Skipping empty row {"excel_row_number":25,"index":23} 
[2025-06-03 08:57:41] local.INFO: Staff bulk import completed {"import_id":15,"processed_rows":12,"skipped_rows":1,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 08:57:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:27:43 +0630","user_id":1} 
[2025-06-03 08:59:17] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:29:16 +0630","user_id":1} 
[2025-06-03 08:59:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:29:19 +0630","user_id":1} 
[2025-06-03 08:59:34] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:29:34 +0630","user_id":1} 
[2025-06-03 08:59:54] local.INFO: Processing Staff Excel data {"total_rows":25,"error_rows":[11,12,14,15,17,18,19,20,21,22,23,24,25],"error_rows_count":13,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":11,"index":9} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":12,"index":10} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":14,"index":12} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":15,"index":13} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":17,"index":15} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":18,"index":16} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":19,"index":17} 
[2025-06-03 08:59:55] local.INFO: Skipping row with errors {"excel_row_number":20,"index":18} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":21,"index":19} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":22,"index":20} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":23,"index":21} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":24,"index":22} 
[2025-06-03 08:59:55] local.INFO: Skipping empty row {"excel_row_number":25,"index":23} 
[2025-06-03 08:59:55] local.INFO: Staff bulk import completed {"import_id":16,"processed_rows":12,"skipped_rows":1,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 08:59:56] local.ERROR: Undefined variable $totalRows {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-view.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#570</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"25 characters\">Staff Bulk Import Results</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import_id":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">16</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Import</span> {<a class=sf-dump-ref>#608</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>16</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">nl_batch_template.xlsx</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 08:59:54</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 08:59:54</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>16</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">nl_batch_template.xlsx</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 08:59:54</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 08:59:54</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#615</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [ &#8230;25]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [ &#8230;25]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">file_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">imports</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","selectedProvider":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\User</span> {<a class=sf-dump-ref>#615</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sahil</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"28 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"8 characters\">provider</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$SZzen8J1KVakBYIonkfVqOvESRiLKhjURT4kABS6C2Ti5aX6/b7hq</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:20</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:59</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">panchal</span>\"
    \"<span class=sf-dump-key>printed_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sahil</span>\"
    \"<span class=sf-dump-key>clinic_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">sahil&#039; clinic</span>\"
    \"<span class=sf-dump-key>NPI#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>LIC#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">985625412</span>\"
    \"<span class=sf-dump-key>DEA#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SR1452632</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>fax</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">B-402 Pavitra flat , nr Aatmiya flat, maneja crossing, vadodara</span>\"
    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Maneja crossing</span>\"
    \"<span class=sf-dump-key>zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">390013</span>\"
    \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"55 characters\">signatures/thgUpSP3IT81Ly1pfm5iSusbEsT5HPsNjlJ1AzYa.png</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:59</span>\"
    \"<span class=sf-dump-key>is_password_reset</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sahil</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"28 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"8 characters\">provider</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$SZzen8J1KVakBYIonkfVqOvESRiLKhjURT4kABS6C2Ti5aX6/b7hq</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:20</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:59</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">panchal</span>\"
    \"<span class=sf-dump-key>printed_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sahil</span>\"
    \"<span class=sf-dump-key>clinic_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">sahil&#039; clinic</span>\"
    \"<span class=sf-dump-key>NPI#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>LIC#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">985625412</span>\"
    \"<span class=sf-dump-key>DEA#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SR1452632</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>fax</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">B-402 Pavitra flat , nr Aatmiya flat, maneja crossing, vadodara</span>\"
    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Maneja crossing</span>\"
    \"<span class=sf-dump-key>zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">390013</span>\"
    \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"55 characters\">signatures/thgUpSP3IT81Ly1pfm5iSusbEsT5HPsNjlJ1AzYa.png</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:59</span>\"
    \"<span class=sf-dump-key>is_password_reset</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">email_verified_at</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">printed_name</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">clinic_name</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"4 characters\">NPI#</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"4 characters\">LIC#</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"4 characters\">DEA#</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3 characters\">fax</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"4 characters\">city</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"3 characters\">zip</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"9 characters\">signature</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"19 characters\">password_changed_at</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"17 characters\">is_password_reset</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","has_back":"<pre class=sf-dump id=sf-dump-1723752402 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/staff-bulk-import</span>\"
</pre><script>Sfdump(\"sf-dump-1723752402\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","scriptsCount":"<pre class=sf-dump id=sf-dump-866233900 data-indent-pad=\"  \"><span class=sf-dump-num>12</span>
</pre><script>Sfdump(\"sf-dump-866233900\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","statusCounts":"<pre class=sf-dump id=sf-dump-2124913676 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>New</span>\" => <span class=sf-dump-num>12</span>
</samp>]
</pre><script>Sfdump(\"sf-dump-2124913676\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentStep":"<pre class=sf-dump id=sf-dump-613111435 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-613111435\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","totalSteps":"<pre class=sf-dump id=sf-dump-1798193917 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-1798193917\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $totalRows at C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-view.blade.php:55)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $totalRows at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\36c49d78d3a4b2bd662d5a047dc45055.php:54)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 54)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\36c49d78d3a4b2bd662d5a047dc45055.php(54): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 54)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#64 {main}
"} 
[2025-06-03 09:00:59] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:30:58 +0630","user_id":1} 
[2025-06-03 09:01:09] local.INFO: Blank row detected {"row_number":2,"consecutive_blank_rows":1} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":2,"column_count":16,"has_data":false,"highest_column_index":16,"consecutive_blank_rows":1} 
[2025-06-03 09:01:09] local.INFO: Blank row detected {"row_number":3,"consecutive_blank_rows":2} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":3,"column_count":16,"has_data":false,"highest_column_index":16,"consecutive_blank_rows":2} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":4,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":5,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":6,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":7,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":8,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":9,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":10,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":11,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":12,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Processing row {"row_number":13,"column_count":16,"has_data":true,"highest_column_index":16,"consecutive_blank_rows":0} 
[2025-06-03 09:01:09] local.INFO: Error statistics {"total_rows":12,"error_rows":3,"valid_rows":9} 
[2025-06-03 09:01:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:31:11 +0630","user_id":4} 
[2025-06-03 09:01:17] local.INFO: Processing Excel data {"total_rows":12,"error_rows":[2,3,4],"error_rows_count":3} 
[2025-06-03 09:01:17] local.INFO: Filtering out error rows before processing  
[2025-06-03 09:01:17] local.INFO: Skipping empty row {"excel_row_number":2,"index":0} 
[2025-06-03 09:01:17] local.INFO: Skipping empty row {"excel_row_number":3,"index":1} 
[2025-06-03 09:01:17] local.INFO: Skipping row with errors {"excel_row_number":4,"index":2} 
[2025-06-03 09:01:18] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":1,"import_id":17} 
[2025-06-03 09:01:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:31:19 +0630","user_id":4} 
[2025-06-03 09:01:34] local.INFO: Update Status Request {"import_id":"17","ids":null,"status":"Pending Approval"} 
[2025-06-03 09:01:34] local.INFO: Processing all records for import {"import_id":"17"} 
[2025-06-03 09:01:34] local.INFO: Filtering for New status only  
[2025-06-03 09:01:34] local.INFO: Update Status Query {"sql":"select * from `import_files` where `import_id` = ? and `status` = ?","bindings":["17","New"]} 
[2025-06-03 09:01:34] local.INFO: Records that will be updated {"record_ids":[143,144,145,146,147,148,149,150,151],"count":9} 
[2025-06-03 09:01:34] local.INFO: User information for signature {"user_id":4,"user_name":"ashish parmar","has_signature":"Yes","signature_path":"signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png"} 
[2025-06-03 09:01:34] local.INFO: Signature image loaded successfully {"user_id":4,"signature_path":"signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png","mime_type":"image/png"} 
[2025-06-03 09:01:34] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-03 14:31:19 +0630"} 
[2025-06-03 09:01:34] local.INFO: Starting PDF regeneration for 9 files  
[2025-06-03 09:01:35] local.INFO: Processed 5 of 9 PDFs  
[2025-06-03 09:01:36] local.INFO: Completed PDF regeneration. Processed 9 of 9 files  
[2025-06-03 09:01:36] local.INFO: ScriptStatusChanged event dispatched {"user_id":4,"user_name":"ashish parmar","count":9} 
[2025-06-03 09:01:39] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:31:38 +0630","user_id":4} 
[2025-06-03 09:01:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:31:48 +0630","user_id":4} 
[2025-06-03 09:02:03] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:32:02 +0630","user_id":1} 
[2025-06-03 09:02:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:32:22 +0630","user_id":1} 
[2025-06-03 09:02:41] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:32:40 +0630","user_id":1} 
[2025-06-03 09:02:42] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"4","selected_provider_name":"ashish parmar"} 
[2025-06-03 09:02:43] local.INFO: Staff bulk import completed {"import_id":18,"processed_rows":9,"skipped_rows":0,"provider_id":"4","provider_name":"aashish"} 
[2025-06-03 09:02:43] local.ERROR: Undefined variable $totalRows {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-view.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#570</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"25 characters\">Staff Bulk Import Results</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import_id":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">18</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Import</span> {<a class=sf-dump-ref>#608</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>18</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">nl_batch_template.xlsx</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:02:42</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:02:42</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>4</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>18</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">nl_batch_template.xlsx</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:02:42</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:02:42</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>4</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#615</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [ &#8230;25]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [ &#8230;25]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">file_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">imports</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","selectedProvider":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\User</span> {<a class=sf-dump-ref>#615</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">ashish</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"26 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"8 characters\">provider</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$wBrV9gZrBx0fluC3YI7/Q.hWw5GVFB.5rZ86WDV2qk.K0A.AyS6w2</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">Vh1voIrAZju94KLuAJGA7Nrv3TH5SpQmSRrgDTPFiGydFOe9gHi9FuhR8kLg</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:19:09</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:20:11</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">parmar</span>\"
    \"<span class=sf-dump-key>printed_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">aashish</span>\"
    \"<span class=sf-dump-key>clinic_name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Ashish&#039;s clinic</span>\"
    \"<span class=sf-dump-key>NPI#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>LIC#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>DEA#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">QW1452368</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>fax</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>41</span>
    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">B-402 Pavitra flat , nr Aatmiya flat, maneja crossing, vadodara</span>\"
    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Maneja crossing</span>\"
    \"<span class=sf-dump-key>zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">390013</span>\"
    \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"55 characters\">signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:20:11</span>\"
    \"<span class=sf-dump-key>is_password_reset</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">ashish</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"26 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"8 characters\">provider</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$wBrV9gZrBx0fluC3YI7/Q.hWw5GVFB.5rZ86WDV2qk.K0A.AyS6w2</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">Vh1voIrAZju94KLuAJGA7Nrv3TH5SpQmSRrgDTPFiGydFOe9gHi9FuhR8kLg</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:19:09</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:20:11</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">parmar</span>\"
    \"<span class=sf-dump-key>printed_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">aashish</span>\"
    \"<span class=sf-dump-key>clinic_name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Ashish&#039;s clinic</span>\"
    \"<span class=sf-dump-key>NPI#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>LIC#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>DEA#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">QW1452368</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>fax</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>41</span>
    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">B-402 Pavitra flat , nr Aatmiya flat, maneja crossing, vadodara</span>\"
    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Maneja crossing</span>\"
    \"<span class=sf-dump-key>zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">390013</span>\"
    \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"55 characters\">signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:20:11</span>\"
    \"<span class=sf-dump-key>is_password_reset</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">email_verified_at</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">printed_name</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">clinic_name</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"4 characters\">NPI#</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"4 characters\">LIC#</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"4 characters\">DEA#</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3 characters\">fax</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"4 characters\">city</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"3 characters\">zip</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"9 characters\">signature</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"19 characters\">password_changed_at</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"17 characters\">is_password_reset</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","has_back":"<pre class=sf-dump id=sf-dump-2047570083 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/staff-bulk-import</span>\"
</pre><script>Sfdump(\"sf-dump-2047570083\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","scriptsCount":"<pre class=sf-dump id=sf-dump-1307047799 data-indent-pad=\"  \"><span class=sf-dump-num>9</span>
</pre><script>Sfdump(\"sf-dump-1307047799\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","statusCounts":"<pre class=sf-dump id=sf-dump-1568839964 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>New</span>\" => <span class=sf-dump-num>9</span>
</samp>]
</pre><script>Sfdump(\"sf-dump-1568839964\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentStep":"<pre class=sf-dump id=sf-dump-262823610 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-262823610\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","totalSteps":"<pre class=sf-dump id=sf-dump-1919296006 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-1919296006\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $totalRows at C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-view.blade.php:55)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $totalRows at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\36c49d78d3a4b2bd662d5a047dc45055.php:54)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 54)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\36c49d78d3a4b2bd662d5a047dc45055.php(54): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 54)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#64 {main}
"} 
[2025-06-03 09:02:47] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:32:46 +0630","user_id":1} 
[2025-06-03 09:03:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:33:04 +0630","user_id":1} 
[2025-06-03 09:03:16] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:33:15 +0630","user_id":1} 
[2025-06-03 09:03:17] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 09:03:18] local.INFO: Staff bulk import completed {"import_id":19,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 09:03:18] local.ERROR: Undefined variable $totalRows {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-view.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-37276745 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#570</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-37276745\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"25 characters\">Staff Bulk Import Results</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import_id":"<pre class=sf-dump id=sf-dump-69925084 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">19</span>\"
</pre><script>Sfdump(\"sf-dump-69925084\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import":"<pre class=sf-dump id=sf-dump-712533576 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Import</span> {<a class=sf-dump-ref>#608</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">nl_batch_template.xlsx</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:03:17</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:03:17</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">nl_batch_template.xlsx</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:03:17</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:03:17</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#615</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [ &#8230;25]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [ &#8230;25]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">file_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">imports</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-712533576\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","selectedProvider":"<pre class=sf-dump id=sf-dump-468205881 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\User</span> {<a class=sf-dump-ref>#615</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sahil</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"28 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"8 characters\">provider</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$SZzen8J1KVakBYIonkfVqOvESRiLKhjURT4kABS6C2Ti5aX6/b7hq</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:20</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:59</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">panchal</span>\"
    \"<span class=sf-dump-key>printed_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sahil</span>\"
    \"<span class=sf-dump-key>clinic_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">sahil&#039; clinic</span>\"
    \"<span class=sf-dump-key>NPI#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>LIC#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">985625412</span>\"
    \"<span class=sf-dump-key>DEA#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SR1452632</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>fax</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">B-402 Pavitra flat , nr Aatmiya flat, maneja crossing, vadodara</span>\"
    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Maneja crossing</span>\"
    \"<span class=sf-dump-key>zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">390013</span>\"
    \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"55 characters\">signatures/thgUpSP3IT81Ly1pfm5iSusbEsT5HPsNjlJ1AzYa.png</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:59</span>\"
    \"<span class=sf-dump-key>is_password_reset</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sahil</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"28 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"8 characters\">provider</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$SZzen8J1KVakBYIonkfVqOvESRiLKhjURT4kABS6C2Ti5aX6/b7hq</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:20</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:59</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">panchal</span>\"
    \"<span class=sf-dump-key>printed_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sahil</span>\"
    \"<span class=sf-dump-key>clinic_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">sahil&#039; clinic</span>\"
    \"<span class=sf-dump-key>NPI#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>LIC#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">985625412</span>\"
    \"<span class=sf-dump-key>DEA#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SR1452632</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>fax</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">B-402 Pavitra flat , nr Aatmiya flat, maneja crossing, vadodara</span>\"
    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Maneja crossing</span>\"
    \"<span class=sf-dump-key>zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">390013</span>\"
    \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"55 characters\">signatures/thgUpSP3IT81Ly1pfm5iSusbEsT5HPsNjlJ1AzYa.png</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-02 12:22:59</span>\"
    \"<span class=sf-dump-key>is_password_reset</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">email_verified_at</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">printed_name</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">clinic_name</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"4 characters\">NPI#</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"4 characters\">LIC#</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"4 characters\">DEA#</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3 characters\">fax</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"4 characters\">city</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"3 characters\">zip</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"9 characters\">signature</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"19 characters\">password_changed_at</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"17 characters\">is_password_reset</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-468205881\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","has_back":"<pre class=sf-dump id=sf-dump-1934277399 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/staff-bulk-import</span>\"
</pre><script>Sfdump(\"sf-dump-1934277399\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","scriptsCount":"<pre class=sf-dump id=sf-dump-1286666513 data-indent-pad=\"  \"><span class=sf-dump-num>9</span>
</pre><script>Sfdump(\"sf-dump-1286666513\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","statusCounts":"<pre class=sf-dump id=sf-dump-683961516 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>New</span>\" => <span class=sf-dump-num>9</span>
</samp>]
</pre><script>Sfdump(\"sf-dump-683961516\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentStep":"<pre class=sf-dump id=sf-dump-191064626 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-191064626\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","totalSteps":"<pre class=sf-dump id=sf-dump-2130789955 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-2130789955\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $totalRows at C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-view.blade.php:55)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $totalRows at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\36c49d78d3a4b2bd662d5a047dc45055.php:54)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 54)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\36c49d78d3a4b2bd662d5a047dc45055.php(54): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 54)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#64 {main}
"} 
[2025-06-03 09:03:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:33:37 +0630","user_id":1} 
[2025-06-03 09:05:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:35:42 +0630","user_id":1} 
[2025-06-03 09:05:51] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:35:50 +0630","user_id":1} 
[2025-06-03 09:05:53] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 09:05:54] local.INFO: Staff bulk import completed {"import_id":20,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 09:05:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:35:56 +0630","user_id":1} 
[2025-06-03 09:06:58] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:36:57 +0630","user_id":1} 
[2025-06-03 09:08:08] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:38:08 +0630","user_id":1} 
[2025-06-03 09:08:27] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:38:26 +0630","user_id":1} 
[2025-06-03 09:08:59] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:38:58 +0630","user_id":1} 
[2025-06-03 09:11:00] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:40:59 +0630","user_id":1} 
[2025-06-03 09:11:34] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:41:34 +0630","user_id":1} 
[2025-06-03 09:11:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:41:43 +0630","user_id":1} 
[2025-06-03 09:11:51] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:41:50 +0630","user_id":1} 
[2025-06-03 09:11:59] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:41:58 +0630","user_id":1} 
[2025-06-03 09:14:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:44:30 +0630","user_id":1} 
[2025-06-03 09:14:47] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:44:46 +0630","user_id":1} 
[2025-06-03 09:14:57] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"4","selected_provider_name":"ashish parmar"} 
[2025-06-03 09:14:59] local.INFO: Staff bulk import completed {"import_id":21,"processed_rows":9,"skipped_rows":0,"provider_id":"4","provider_name":"aashish"} 
[2025-06-03 09:15:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:45:02 +0630","user_id":1} 
[2025-06-03 09:16:20] local.ERROR: Undefined variable $totalRows {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-view.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-151944206 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#570</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-151944206\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-949736508 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"25 characters\">Staff Bulk Import Results</span>\"
</pre><script>Sfdump(\"sf-dump-949736508\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import_id":"<pre class=sf-dump id=sf-dump-1617489580 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"2 characters\">21</span>\"
</pre><script>Sfdump(\"sf-dump-1617489580\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","import":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Import</span> {<a class=sf-dump-ref>#608</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>21</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">nl_batch_template.xlsx</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:14:57</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:14:57</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>4</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>21</span>
    \"<span class=sf-dump-key>file_name</span>\" => \"<span class=sf-dump-str title=\"22 characters\">nl_batch_template.xlsx</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:14:57</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 09:14:57</span>\"
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>4</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#615</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [ &#8230;25]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [ &#8230;25]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [ &#8230;20]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">file_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"7 characters\">imports</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","selectedProvider":"<pre class=sf-dump id=sf-dump-41967784 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\User</span> {<a class=sf-dump-ref>#615</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">?</span>
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">ashish</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"26 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"8 characters\">provider</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$wBrV9gZrBx0fluC3YI7/Q.hWw5GVFB.5rZ86WDV2qk.K0A.AyS6w2</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">Vh1voIrAZju94KLuAJGA7Nrv3TH5SpQmSRrgDTPFiGydFOe9gHi9FuhR8kLg</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:19:09</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:20:11</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">parmar</span>\"
    \"<span class=sf-dump-key>printed_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">aashish</span>\"
    \"<span class=sf-dump-key>clinic_name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Ashish&#039;s clinic</span>\"
    \"<span class=sf-dump-key>NPI#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>LIC#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>DEA#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">QW1452368</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>fax</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>41</span>
    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">B-402 Pavitra flat , nr Aatmiya flat, maneja crossing, vadodara</span>\"
    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Maneja crossing</span>\"
    \"<span class=sf-dump-key>zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">390013</span>\"
    \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"55 characters\">signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:20:11</span>\"
    \"<span class=sf-dump-key>is_password_reset</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">ashish</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"26 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"8 characters\">provider</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$wBrV9gZrBx0fluC3YI7/Q.hWw5GVFB.5rZ86WDV2qk.K0A.AyS6w2</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"60 characters\">Vh1voIrAZju94KLuAJGA7Nrv3TH5SpQmSRrgDTPFiGydFOe9gHi9FuhR8kLg</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:19:09</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:20:11</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">parmar</span>\"
    \"<span class=sf-dump-key>printed_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">aashish</span>\"
    \"<span class=sf-dump-key>clinic_name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Ashish&#039;s clinic</span>\"
    \"<span class=sf-dump-key>NPI#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>LIC#</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>DEA#</span>\" => \"<span class=sf-dump-str title=\"9 characters\">QW1452368</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>fax</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>41</span>
    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"63 characters\">B-402 Pavitra flat , nr Aatmiya flat, maneja crossing, vadodara</span>\"
    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Maneja crossing</span>\"
    \"<span class=sf-dump-key>zip</span>\" => \"<span class=sf-dump-str title=\"6 characters\">390013</span>\"
    \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"55 characters\">signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png</span>\"
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-03 04:20:11</span>\"
    \"<span class=sf-dump-key>is_password_reset</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>remember_token</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"
    \"<span class=sf-dump-key>password_changed_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">email_verified_at</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">printed_name</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">clinic_name</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"4 characters\">NPI#</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"4 characters\">LIC#</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"4 characters\">DEA#</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3 characters\">fax</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">address</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"4 characters\">city</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"3 characters\">zip</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"9 characters\">signature</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"19 characters\">password_changed_at</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"17 characters\">is_password_reset</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  +<span class=sf-dump-public title=\"Public property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-41967784\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","has_back":"<pre class=sf-dump id=sf-dump-313801503 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/staff-bulk-import</span>\"
</pre><script>Sfdump(\"sf-dump-313801503\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","scriptsCount":"<pre class=sf-dump id=sf-dump-155293497 data-indent-pad=\"  \"><span class=sf-dump-num>9</span>
</pre><script>Sfdump(\"sf-dump-155293497\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","statusCounts":"<pre class=sf-dump id=sf-dump-1148313657 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>New</span>\" => <span class=sf-dump-num>9</span>
</samp>]
</pre><script>Sfdump(\"sf-dump-1148313657\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentStep":"<pre class=sf-dump id=sf-dump-1696669738 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-1696669738\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","totalSteps":"<pre class=sf-dump id=sf-dump-528743006 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-528743006\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $totalRows at C:\\KodeCreators\\newlife-panel\\resources\\views\\excel-import\\staff-view.blade.php:62)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $totalRows at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\36c49d78d3a4b2bd662d5a047dc45055.php:61)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 61)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\36c49d78d3a4b2bd662d5a047dc45055.php(61): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 61)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#64 {main}
"} 
[2025-06-03 09:17:00] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:46:59 +0630","user_id":1} 
[2025-06-03 09:25:07] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:55:06 +0630","user_id":1} 
[2025-06-03 09:26:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:56:56 +0630","user_id":1} 
[2025-06-03 09:28:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:58:22 +0630","user_id":1} 
[2025-06-03 09:28:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:58:55 +0630","user_id":1} 
[2025-06-03 09:29:01] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:59:00 +0630","user_id":1} 
[2025-06-03 09:29:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:59:11 +0630","user_id":1} 
[2025-06-03 09:29:17] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 09:29:17] local.INFO: Staff bulk import completed {"import_id":22,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 09:29:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:59:19 +0630","user_id":1} 
[2025-06-03 09:29:34] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:59:33 +0630","user_id":1} 
[2025-06-03 09:29:45] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:59:45 +0630","user_id":4} 
[2025-06-03 09:29:49] local.INFO: Device time stored in session {"device_time":"2025-06-03 14:59:47 +0630","user_id":4} 
[2025-06-03 09:39:01] local.INFO: Device time stored in session {"device_time":"2025-06-03 15:09:00 +0630","user_id":1} 
[2025-06-04 04:48:24] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:18:23 +0630","user_id":1} 
[2025-06-04 04:53:33] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:23:32 +0630","user_id":1} 
[2025-06-04 04:53:36] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:23:35 +0630","user_id":1} 
[2025-06-04 05:04:57] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'newlife.system_logs' doesn't exist (Connection: mysql, SQL: insert into `system_logs` (`timestamp`, `type`, `user_type`, `username`, `user_id`, `message`, `context`, `ip_address`, `user_agent`, `updated_at`, `created_at`) values (2025-06-04 05:04:57, login, administrator, Admin User, 1, User logged in, ?, 127.0.0.1, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, 2025-06-04 05:04:57, 2025-06-04 05:04:57)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'newlife.system_logs' doesn't exist (Connection: mysql, SQL: insert into `system_logs` (`timestamp`, `type`, `user_type`, `username`, `user_id`, `message`, `context`, `ip_address`, `user_agent`, `updated_at`, `created_at`) values (2025-06-04 05:04:57, login, administrator, Admin User, 1, User logged in, ?, 127.0.0.1, Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36, 2025-06-04 05:04:57, 2025-06-04 05:04:57)) at C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `sy...', Array, Object(Closure))
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `sy...', Array, Object(Closure))
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `sy...', Array, 'id')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `sy...', Array, 'id')
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\SystemLog))
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\SystemLog), Object(Closure))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\KodeCreators\\newlife-panel\\app\\Services\\LogService.php(279): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\KodeCreators\\newlife-panel\\app\\Services\\LogService.php(44): App\\Services\\LogService::createLog(Array)
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Controllers\\Auth\\LoginController.php(92): App\\Services\\LogService::logLogin(Object(App\\Models\\Admin))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(114): App\\Http\\Controllers\\Auth\\LoginController->authenticated(Object(Illuminate\\Http\\Request), Object(App\\Models\\Admin))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(51): App\\Http\\Controllers\\Auth\\LoginController->sendLoginResponse(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('login', Array)
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#70 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'newlife.system_logs' doesn't exist at C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:39)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(39): PDO->prepare('insert into `sy...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `sy...', Array)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `sy...', Array, Object(Closure))
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `sy...', Array, Object(Closure))
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `sy...', Array, 'id')
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `sy...', Array, 'id')
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\SystemLog))
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\SystemLog), Object(Closure))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\KodeCreators\\newlife-panel\\app\\Services\\LogService.php(279): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\KodeCreators\\newlife-panel\\app\\Services\\LogService.php(44): App\\Services\\LogService::createLog(Array)
#18 C:\\KodeCreators\\newlife-panel\\app\\Http\\Controllers\\Auth\\LoginController.php(92): App\\Services\\LogService::logLogin(Object(App\\Models\\Admin))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(114): App\\Http\\Controllers\\Auth\\LoginController->authenticated(Object(Illuminate\\Http\\Request), Object(App\\Models\\Admin))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(51): App\\Http\\Controllers\\Auth\\LoginController->sendLoginResponse(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('login', Array)
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#72 {main}
"} 
[2025-06-04 05:05:20] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:35:20 +0630","user_id":1} 
[2025-06-04 05:05:50] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:35:49 +0630","user_id":1} 
[2025-06-04 05:07:49] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:37:48 +0630","user_id":1} 
[2025-06-04 05:07:54] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:37:54 +0630","user_id":1} 
[2025-06-04 05:07:58] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:37:57 +0630","user_id":1} 
[2025-06-04 05:08:01] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:38:00 +0630","user_id":1} 
[2025-06-04 05:08:04] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:38:03 +0630","user_id":1} 
[2025-06-04 05:21:43] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:51:42 +0630","user_id":1} 
[2025-06-04 05:21:49] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:51:48 +0630","user_id":1} 
[2025-06-04 05:21:51] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:51:50 +0630","user_id":1} 
[2025-06-04 05:22:02] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:52:01 +0630","user_id":1} 
[2025-06-04 05:22:06] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:52:05 +0630","user_id":1} 
[2025-06-04 05:22:09] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:52:09 +0630","user_id":1} 
[2025-06-04 05:22:17] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:52:16 +0630","user_id":1} 
[2025-06-04 05:22:32] local.INFO: Device time stored in session {"device_time":"2025-06-04 10:52:31 +0630","user_id":1} 
[2025-06-04 05:22:40] local.INFO: Script returned for revision and data after regeneration {"import_file_id":149,"file_name":"prescription_10_Teri_Degiacomo.pdf","file_path":"public/prescriptions/17/prescription_10_Teri_Degiacomo.pdf","reason":"test"} 
[2025-06-04 05:45:31] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:15:30 +0630","user_id":1} 
[2025-06-04 05:45:35] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:15:35 +0630","user_id":1} 
[2025-06-04 05:45:42] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:15:41 +0630","user_id":1} 
[2025-06-04 05:45:48] local.INFO: Fax sent successfully {"to":["+18669938841","+18885110528"],"from":"+18669938841","file":"/storage/transient-ca2119c6c00d4d05ad28429d1fec8b54.pdf"} 
[2025-06-04 05:45:51] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:15:51 +0630","user_id":1} 
[2025-06-04 05:45:57] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:15:56 +0630","user_id":1} 
[2025-06-04 05:46:00] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:16:00 +0630","user_id":1} 
[2025-06-04 05:46:04] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:16:03 +0630","user_id":1} 
[2025-06-04 05:46:44] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:16:43 +0630","user_id":1} 
[2025-06-04 05:46:49] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:16:47 +0630","user_id":1} 
[2025-06-04 05:54:53] local.ERROR: The "--SystemLogSeeder" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--SystemLogSeeder\" option does not exist. at C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(152): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('SystemLogSeeder', NULL)
#1 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--SystemLogSeed...')
#2 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--SystemLogSeed...', true)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\KodeCreators\\newlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-06-04 05:55:00] local.ERROR: The "-S" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"-S\" option does not exist. at C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:125)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(107): Symfony\\Component\\Console\\Input\\ArgvInput->parseShortOptionSet('SystemLogSeeder')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(87): Symfony\\Component\\Console\\Input\\ArgvInput->parseShortOption('-SystemLogSeede...')
#2 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('-SystemLogSeede...', true)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\KodeCreators\\newlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-06-04 05:56:18] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:26:17 +0630","user_id":1} 
[2025-06-04 05:57:04] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:27:03 +0630","user_id":3} 
[2025-06-04 06:03:54] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:33:53 +0630","user_id":3} 
[2025-06-04 06:03:58] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:33:57 +0630","user_id":3} 
[2025-06-04 06:04:00] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:33:59 +0630","user_id":3} 
[2025-06-04 06:04:13] local.INFO: Using client timestamp for signed_at {"timestamp":"2025-06-04 11:34:13 +0630"} 
[2025-06-04 06:04:13] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":188,"signed_at_db":"2025-06-04 11:34:13","formatted_signed_at":"06/04/2025 11:34 AM"} 
[2025-06-04 06:04:16] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:34:15 +0630","user_id":3} 
[2025-06-04 06:04:27] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:34:25 +0630","user_id":1} 
[2025-06-04 06:05:52] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:35:51 +0630","user_id":1} 
[2025-06-04 06:07:49] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:37:48 +0630","user_id":1} 
[2025-06-04 06:18:06] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:48:05 +0630","user_id":1} 
[2025-06-04 06:18:54] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:48:52 +0630","user_id":1} 
[2025-06-04 06:19:09] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:49:08 +0630","user_id":1} 
[2025-06-04 06:19:21] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:49:19 +0630","user_id":1} 
[2025-06-04 06:19:34] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:49:32 +0630","user_id":1} 
[2025-06-04 06:20:44] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:50:42 +0630","user_id":1} 
[2025-06-04 06:21:22] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:51:20 +0630","user_id":1} 
[2025-06-04 06:23:20] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:53:18 +0630","user_id":1} 
[2025-06-04 06:25:02] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:55:01 +0630","user_id":1} 
[2025-06-04 06:27:21] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:57:20 +0630","user_id":1} 
[2025-06-04 06:28:29] local.INFO: Device time stored in session {"device_time":"2025-06-04 11:58:27 +0630","user_id":1} 
[2025-06-04 06:31:31] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:01:29 +0630","user_id":1} 
[2025-06-04 06:36:55] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:06:54 +0630","user_id":1} 
[2025-06-04 06:37:24] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:07:23 +0630","user_id":1} 
[2025-06-04 06:37:58] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:07:57 +0630","user_id":1} 
[2025-06-04 06:38:44] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:08:43 +0630","user_id":1} 
[2025-06-04 06:43:52] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:13:51 +0630","user_id":1} 
[2025-06-04 06:44:40] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:14:38 +0630","user_id":1} 
[2025-06-04 06:45:19] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:15:18 +0630","user_id":1} 
[2025-06-04 06:51:49] local.INFO: LogController Debug: {"all_params":{"pagination":{"page":"1","pages":"1","perpage":"25","total":"12"},"query":null},"query_params":null,"direct_type":null,"direct_user_type":null,"query_type":null,"query_user_type":null} 
[2025-06-04 06:51:49] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:21:47 +0630","user_id":1} 
[2025-06-04 06:51:54] local.INFO: LogController Debug: {"all_params":{"pagination":{"page":"1","pages":"1","perpage":"25","total":"12"},"query":{"query":{"type":"user_action","user_type":null,"date_from":null,"date_to":null,"search":null}}},"query_params":{"query":{"type":"user_action","user_type":null,"date_from":null,"date_to":null,"search":null}},"direct_type":null,"direct_user_type":null,"query_type":null,"query_user_type":null} 
[2025-06-04 06:51:57] local.INFO: LogController Debug: {"all_params":{"pagination":{"page":"1","pages":"1","perpage":"25","total":"12"},"query":{"query":{"type":"system_error","user_type":null,"date_from":null,"date_to":null,"search":null}}},"query_params":{"query":{"type":"system_error","user_type":null,"date_from":null,"date_to":null,"search":null}},"direct_type":null,"direct_user_type":null,"query_type":null,"query_user_type":null} 
[2025-06-04 06:51:58] local.INFO: LogController Debug: {"all_params":{"pagination":{"page":"1","pages":"1","perpage":"25","total":"12"},"query":{"query":{"type":null,"user_type":null,"date_from":null,"date_to":null,"search":null}}},"query_params":{"query":{"type":null,"user_type":null,"date_from":null,"date_to":null,"search":null}},"direct_type":null,"direct_user_type":null,"query_type":null,"query_user_type":null} 
[2025-06-04 06:52:02] local.INFO: LogController Debug: {"all_params":{"pagination":{"page":"1","pages":"1","perpage":"25","total":"12"},"query":{"query":{"type":null,"user_type":"administrator","date_from":null,"date_to":null,"search":null}}},"query_params":{"query":{"type":null,"user_type":"administrator","date_from":null,"date_to":null,"search":null}},"direct_type":null,"direct_user_type":null,"query_type":null,"query_user_type":null} 
[2025-06-04 06:52:10] local.INFO: LogController Debug: {"all_params":{"pagination":{"page":"1","pages":"1","perpage":"25","total":"12"},"query":{"query":{"type":null,"user_type":"operator","date_from":null,"date_to":null,"search":null}}},"query_params":{"query":{"type":null,"user_type":"operator","date_from":null,"date_to":null,"search":null}},"direct_type":null,"direct_user_type":null,"query_type":null,"query_user_type":null} 
[2025-06-04 06:52:12] local.INFO: LogController Debug: {"all_params":{"pagination":{"page":"1","pages":"1","perpage":"25","total":"12"},"query":{"query":{"type":null,"user_type":null,"date_from":null,"date_to":null,"search":null}}},"query_params":{"query":{"type":null,"user_type":null,"date_from":null,"date_to":null,"search":null}},"direct_type":null,"direct_user_type":null,"query_type":null,"query_user_type":null} 
[2025-06-04 06:52:15] local.INFO: LogController Debug: {"all_params":{"pagination":{"page":"1","pages":"1","perpage":"25","total":"12"},"query":{"query":{"type":null,"user_type":"provider","date_from":null,"date_to":null,"search":null}}},"query_params":{"query":{"type":null,"user_type":"provider","date_from":null,"date_to":null,"search":null}},"direct_type":null,"direct_user_type":null,"query_type":null,"query_user_type":null} 
[2025-06-04 06:55:36] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:25:35 +0630","user_id":1} 
[2025-06-04 06:56:17] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:26:16 +0630","user_id":1} 
[2025-06-04 06:58:43] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:28:42 +0630","user_id":1} 
[2025-06-04 07:03:27] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:33:25 +0630","user_id":1} 
[2025-06-04 07:08:14] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:38:12 +0630","user_id":1} 
[2025-06-04 07:10:51] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:40:49 +0630","user_id":1} 
[2025-06-04 07:11:09] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:41:08 +0630","user_id":3} 
[2025-06-04 07:11:12] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:41:11 +0630","user_id":3} 
[2025-06-04 07:18:29] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:48:28 +0630","user_id":1} 
[2025-06-04 07:18:33] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:48:31 +0630","user_id":1} 
[2025-06-04 07:19:24] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:49:23 +0630","user_id":1} 
[2025-06-04 07:19:28] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:49:26 +0630","user_id":1} 
[2025-06-04 07:21:38] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:51:38 +0630","user_id":1} 
[2025-06-04 07:21:44] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:51:44 +0630","user_id":1} 
[2025-06-04 07:21:47] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:51:46 +0630","user_id":1} 
[2025-06-04 07:21:52] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:51:50 +0630","user_id":1} 
[2025-06-04 07:23:54] local.INFO: Device time stored in session {"device_time":"2025-06-04 12:53:53 +0630","user_id":1} 
[2025-06-04 08:44:07] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:14:06 +0630","user_id":1} 
[2025-06-04 08:44:33] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:14:31 +0630","user_id":1} 
[2025-06-04 08:44:45] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:14:44 +0630","user_id":1} 
[2025-06-04 08:45:04] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:15:04 +0630","user_id":3} 
[2025-06-04 08:45:10] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:15:08 +0630","user_id":1} 
[2025-06-04 08:45:18] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:15:18 +0630","user_id":3} 
[2025-06-04 08:46:07] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:16:06 +0630","user_id":3} 
[2025-06-04 08:46:13] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:16:11 +0630","user_id":1} 
[2025-06-04 08:46:30] local.INFO: Using device time from session for signed_at in archive {"timestamp":"2025-06-04 14:16:06 +0630"} 
[2025-06-04 08:46:31] local.INFO: ScriptStatusChanged event dispatched from ArchiveController {"user_id":3,"user_name":"sahil panchal","import_file_id":197} 
[2025-06-04 08:46:31] local.INFO: Generating PDF with signed_at timestamp {"import_file_id":197,"signed_at_db":"2025-06-04 14:16:06","formatted_signed_at":"06/04/2025 02:16 PM"} 
[2025-06-04 08:46:34] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:16:33 +0630","user_id":3} 
[2025-06-04 08:46:42] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:16:40 +0630","user_id":1} 
[2025-06-04 08:46:56] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:16:56 +0630","user_id":3} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-04 08:47:08] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-04 08:47:10] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:17:10 +0630","user_id":3} 
[2025-06-04 08:47:11] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-04 08:47:12] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":24} 
[2025-06-04 08:47:14] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:17:14 +0630","user_id":3} 
[2025-06-04 08:47:21] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:17:19 +0630","user_id":1} 
[2025-06-04 08:47:41] local.INFO: Update Status Request {"import_id":"24","ids":["198","199","201","200"],"status":"Pending Approval"} 
[2025-06-04 08:47:41] local.INFO: Processing specific IDs {"count":4} 
[2025-06-04 08:47:41] local.INFO: Filtering for New status only  
[2025-06-04 08:47:41] local.INFO: Update Status Query {"sql":"select * from `import_files` where `id` in (?, ?, ?, ?) and `status` = ?","bindings":[198,199,201,200,"New"]} 
[2025-06-04 08:47:41] local.INFO: Records that will be updated {"record_ids":[198,199,200,201],"count":4} 
[2025-06-04 08:47:41] local.INFO: User information for signature {"user_id":3,"user_name":"sahil panchal","has_signature":"Yes","signature_path":"signatures/thgUpSP3IT81Ly1pfm5iSusbEsT5HPsNjlJ1AzYa.png"} 
[2025-06-04 08:47:41] local.INFO: Signature image loaded successfully {"user_id":3,"signature_path":"signatures/thgUpSP3IT81Ly1pfm5iSusbEsT5HPsNjlJ1AzYa.png","mime_type":"image/png"} 
[2025-06-04 08:47:41] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-04 14:17:14 +0630"} 
[2025-06-04 08:47:41] local.INFO: Starting PDF regeneration for 4 files  
[2025-06-04 08:47:42] local.INFO: Completed PDF regeneration. Processed 4 of 4 files  
[2025-06-04 08:47:42] local.INFO: ScriptStatusChanged event dispatched {"user_id":3,"user_name":"sahil panchal","count":4} 
[2025-06-04 08:47:45] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:17:44 +0630","user_id":3} 
[2025-06-04 08:47:52] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:17:50 +0630","user_id":1} 
[2025-06-04 08:50:20] local.ERROR: syntax error, unexpected token "<<" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"<<\" at C:\\KodeCreators\\newlife-panel\\app\\Http\\Controllers\\Auth\\LoginController.php:91)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\KodeCreators...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1096): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1035): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(818): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(201): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(146): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 17)
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\KodeCreators\\newlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}
"} 
[2025-06-04 09:00:46] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:30:44 +0630","user_id":1} 
[2025-06-04 09:00:53] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:30:52 +0630","user_id":1} 
[2025-06-04 09:00:56] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:30:56 +0630","user_id":1} 
[2025-06-04 09:01:01] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:31:00 +0630","user_id":1} 
[2025-06-04 09:01:04] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:31:04 +0630","user_id":1} 
[2025-06-04 09:01:09] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:31:08 +0630","user_id":1} 
[2025-06-04 09:01:16] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:31:14 +0630","user_id":1} 
[2025-06-04 09:01:19] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:31:18 +0630","user_id":1} 
[2025-06-04 09:01:24] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:31:22 +0630","user_id":1} 
[2025-06-04 09:01:47] local.INFO: Device time stored in session {"device_time":"2025-06-04 14:31:45 +0630","user_id":1} 
