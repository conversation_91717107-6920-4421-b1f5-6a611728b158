<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ForcePasswordChange
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated
        if (Auth::check()) {
            $user = Auth::user();
            // If password_changed_at is null, user needs to change password
            if (is_null($user->password_changed_at)) {
                if ($user->is_password_reset) {
                    Auth::logout();
                }

                // Allow access to change password route
                if ($request->route()->getName() === 'settings.change-password') {
                    return $next($request);
                }

                // Redirect to change password page for all other routes
                return redirect()->route('settings.change-password')
                    ->with('warning', 'You must change your password before accessing the system.');
            }
        }

        return $next($request);
    }
}
