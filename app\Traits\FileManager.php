<?php

namespace App\Traits;

use App\File;
use App\Models\Family;
use Exception;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Intervention\Image\Facades\Image;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Throwable;
use ZipArchive;
use Webklex\PDFMerger\Facades\PDFMergerFacade as PDFMerger;
use Illuminate\Support\Facades\Log;

trait FileManager
{

    protected function saveFile($file, string $model_name, $is_from_web = false, $extension = 'png')
    {
        // $user = Auth::user();
        // $id = md5($user->id);

        // if (App::environment(['testing', 'local', 'staging'])) {
        //     $model_name = "testing/{$model_name}/{$id}";
        // } else {
        //     //same file structure as testing
        //     $model_name = "{$model_name}/{$id}";
        // }

        // if (in_array(Str::lower($file->getExtension()), ['jpg', 'png', 'jpeg', 'gif'])) {
        //     $model_name = "{$model_name}/";

        //     $resize_file = Image::make($file)->orientate();

        //     $upload_file_name = $this->generateFileName($file, $model_name) . '.' . $file->getExtension();

        //     $path = Storage::put($model_name . $upload_file_name, $resize_file->stream()->__toString());

        //     $path = $model_name . $upload_file_name;

        //     return $path;
        // }
        $filename = Str::random(30);

        if (!$is_from_web && is_object($file) && method_exists($file, 'getClientOriginalExtension')) {
            $extension = $file->getClientOriginalExtension();
        }
        while (Storage::exists("{$model_name}/{$filename}.{$extension}")) {
            $filename = Str::random(30);
        }
        if ($is_from_web) {
            Storage::put("{$model_name}/{$filename}.{$extension}", $file);
        } else {
            Storage::put("{$model_name}/{$filename}.{$extension}", file_get_contents($file->getRealPath()));
        }
        $path = "{$model_name}/{$filename}.{$extension}";
        return $path;
    }

    /**
     * @return string
     */
    protected function generateFileName($file, $path)
    {
        $filename = Str::random(20);

        // Make sure the filename does not exist, if it does, just regenerate
        while (Storage::exists($path . $filename . '.' . $file->getClientOriginalExtension())) {
            $filename = Str::random(20);
        }

        return $filename;
    }

    public function deleteDirectory($path)
    {
        // if (count(Storage::exists($path))) {
        try {
            //code...
            Storage::deleteDirectory($path);
        } catch (\Throwable $th) {
            //throw $th;
        }
        // }
    }

    public function deleteFile($path)
    {
        $normalizedPath = $this->normalizeStoragePath($path);
        if (Storage::exists($normalizedPath)) {
            Storage::delete($normalizedPath);
        }
    }

    protected function saveFileFromUrl($url, string $model_name = "test")
    {
        try {
            $file = file_get_contents($url);
        } catch (Throwable $th) {
            return null;
        }
        $url_array = explode('.', $url);
        $extension = end($url_array);
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $extension = 'jpg';
        }
        $filename = Str::random(30);
        while (Storage::exists("{$model_name}/{$filename}.{$extension}")) {
            $filename = Str::random(30);
        }

        if (Storage::put("{$model_name}/{$filename}.{$extension}", $file)) {
            return "{$model_name}/{$filename}.{$extension}";
        } else {
            null;
        }
    }

    public function createZip(array $metadata)
    {
        $filename = 'data.zip';
        if (isset($metadata['filename'])) {
            $filename = $metadata['filename'];
        }
        $files = [];
        if (isset($metadata['files']) && is_array($metadata['files'])) {
            $files = $metadata['files'];
        }
        $zip = new ZipArchive;
        $zip->open(storage_path($filename), ZipArchive::CREATE);
        foreach ($files as $file) {
            $url = null;
            if (isset($file['url'])) {
                $url = $file['url'];
            }
            $file_content = null;
            if (isset($file['file_content'])) {
                $file_content = $file['file_content'];
            }

            if (!$file_content && !$url) {
                continue;
            }
            if ($file_content) {
                if (!isset($file['filename'])) {
                    continue;
                }
            }

            if ($url) {
                $temp_filename = basename($url);
            }
            if (isset($file['filename'])) {
                $temp_filename = $file['filename'];
            }
            $zip->addFromString(
                $temp_filename,
                $file_content ?? file_get_contents($url)
            );
        }
        $zip->close();
        $data = readfile(storage_path($filename));
        unlink(storage_path($filename));
        return $data;
    }

    public function mergePdfs(array $filePaths, string $outputDirectory = 'temp/merged_pdfs')
    {
        try {
            // Initialize the PDF merger
            $oMerger = PDFMerger::init();

            // Create a unique filename for the merged PDF
            $outputFilename = Str::random(20) . '.pdf';

            // Ensure the output directory exists
            if (!Storage::exists($outputDirectory)) {
                Storage::makeDirectory($outputDirectory);
            }

            $outputPath = "{$outputDirectory}/{$outputFilename}";
            $validPdfsCount = 0;

            // Process each file path
            foreach ($filePaths as $filePath) {
                // Normalize the file path to handle mixed slashes and absolute paths

                $normalizedPath = $filePath;
                if (!Storage::exists($normalizedPath)) {
                    continue;
                }
                $normalizedPath = Storage::path($normalizedPath);

                // Skip if file doesn't exist

                // Get file extension and check if it's a PDF
                $extension = pathinfo($filePath, PATHINFO_EXTENSION);
                if (strtolower($extension) !== 'pdf') {

                    continue;
                }
                // Get the full path to the file in storage
                $fullPath = $normalizedPath;

                // Add the PDF to the merger
                $oMerger->addPDF($fullPath, 'all');
                $validPdfsCount++;
            }

            // If no valid PDFs were found, return null
            if ($validPdfsCount === 0) {

                return null;
            }

            // Merge the PDFs
            $oMerger->merge();

            // Save the merged PDF
            $fullOutputPath = Storage::path($outputPath);
            $oMerger->save($fullOutputPath);

            // Check if the merged PDF was created successfully
            if (!Storage::exists($outputPath)) {
                return null;
            }

            return [
                'success' => true,
                'path' => $outputPath,
                'full_path' => $fullOutputPath,
                'filename' => $outputFilename,
                'merged_count' => $validPdfsCount
            ];
        } catch (Exception $e) {

            Log::error('Failed to merge PDFs', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return null;
        }
    }
    protected function normalizeStoragePath($path)
    {
        // Replace backslashes with forward slashes
        $path = str_replace('\\', '/', $path);

        // Check if this is an absolute path to the storage directory
        $storageBasePath = str_replace('\\', '/', storage_path('app'));

        // If the path starts with the storage base path, make it relative
        if (strpos($path, $storageBasePath) === 0) {
            $path = substr($path, strlen($storageBasePath) + 1); // +1 for the trailing slash
        }

        // If the path starts with 'public/', it's already relative to the storage disk
        // Otherwise, if it starts with '/public/', remove the leading slash
        if (strpos($path, '/public/') === 0) {
            $path = substr($path, 1);
        }

        return $path;
    }
}
