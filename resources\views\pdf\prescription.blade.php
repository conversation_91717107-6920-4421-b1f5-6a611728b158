<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Prescription</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
        }

        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #000;
            padding: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        .header-table td {
            vertical-align: top;
            width: 50%;
        }

        .patient-table {
            width: 100%;
            border: 1px solid #000;
            margin-bottom: 15px;
        }

        .patient-table td {
            padding: 5px;
        }

        .patient-left {
            width: 60%;
            border-right: 1px solid #000;
        }

        .prescription-table {
            width: 100%;
            border: 1px solid #000;
            margin-bottom: 15px;
        }

        .prescription-table td {
            padding: 5px;
        }

        .signature-line {
            border-top: 1px solid #000;
            width: 60%;
            margin-top: 50px;
            margin-bottom: 10px;
        }

        .bold {
            font-weight: bold;
        }

        .prescription-row {
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header Section -->
        <table class="header-table">
            <tr>
                <td style="vertical-align: top;">
                    <div><span class="bold">DEA#</span> {{ $user->{'DEA#'} ?? '' }}</div>
                    <div><span class="bold">LIC#</span> {{ $user->{'LIC#'} ?? '' }}</div>
                    <div><span class="bold">NPI#</span> {{ $user->{'NPI#'} ?? '' }}</div>
                    <div><span class="bold">{{ $user->clinic_name ?? '' }}</span></div>
                    <div><span class="bold">{{ $doctorName ?? '' }}</span></div>
                    <div>{{ $user->address ?? '' }}</div>
                    <div>{{ $user->city ?? '' }}, {{ $userState->short_name ?? '' }} {{ $user->zip ?? '' }}
                    </div>
                    <div><span class="bold">Phone:</span> {{ $user->phone ?? '' }}</div>
                    <div><span class="bold">Fax:</span> {{ $user->fax ?? '' }}</div>
                </td>
                <td style="vertical-align: bottom; padding-top: 30px; text-align: left;">
                    <div><span class="bold">DiRx Inc</span></div>
                    <div>3540 NW 56th St. Suite 204</div>
                    <div>Ft Lauderdale, FL 33309</div>
                    <div><span class="bold">Phone:</span> **********</div>
                    <div><span class="bold">Fax:</span> **********</div>
                </td>
            </tr>
        </table>

        <!-- Patient Information Section -->
        <table class="patient-table" style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
            <!-- Patient Name and Date Row -->
            <tr>
                <td
                    style="width: 70%; border-top: 1px solid #000; border-left: 1px solid #000; border-bottom: 1px solid #000; padding: 5px;">
                    <div><span class="bold">Patient Name:</span> {{ $data[2] . ' ' . $data[1] }}</div>
                </td>
                <td
                    style="width: 30%; border-top: 1px solid #000; border-right: 1px solid #000; border-bottom: 1px solid #000; padding: 5px; text-align: right;">
                    @if (isset($isSigned) && $isSigned)
                        @php
                            if (isset($data[0]) && is_numeric($data[0])) {
                                $scriptDate = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($data[0]);
                                $scriptDateFormatted = $scriptDate->format('m/d/Y');
                            } else {
                                $scriptDateFormatted = $data[0];
                            }
                        @endphp
                        <div><span class="bold">Date:</span> {{ $scriptDateFormatted }}</div>
                    @else
                        @php
                            $excel_serial = $data[0]; // like 26195
                            $date = \Carbon\Carbon::createFromDate(1900, 1, 1)
                                ->addDays($excel_serial - 2)
                                ->format('m/d/Y');
                        @endphp
                        <div><span class="bold">Date:</span> {{ $date }}</div>
                    @endif
                </td>
            </tr>
            <!-- Patient Details Row - Two Column Layout -->
            <tr>
                <td style="width: 50%; border: 1px solid #000; padding: 5px; vertical-align: top;">
                    @if (isset($isSigned) && $isSigned)
                        @php
                            if (isset($data[3])) {
                                if (is_numeric($data[3])) {
                                    // Handle Excel date format (numeric)
                                    $dobDate = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($data[3]);
                                    $dobFormatted = $dobDate->format('m/d/Y');
                                    $age = \Carbon\Carbon::parse($dobDate)->age;
                                } else {
                                    // Handle string date format (like "09/19/1971")
                                    $dobFormatted = $data[3];
                                    try {
                                        $age = \Carbon\Carbon::parse($dobFormatted)->age;
                                    } catch (\Exception $e) {
                                        $age = '';
                                    }
                                }
                            } else {
                                $dobFormatted = '';
                                $age = '';
                            }
                        @endphp
                        <div><span class="bold">DOB:</span> {{ $dobFormatted }}</div>
                        <div><span class="bold">Age:</span> {{ $age }}</div>
                    @else
                        @php
                            $excel_serial = $data[3]; // like 26195
                            $dob_date = \Carbon\Carbon::createFromDate(1900, 1, 1)
                                ->addDays($excel_serial - 2)
                                ->format('m/d/Y');
                            $dob = \Carbon\Carbon::createFromDate(1900, 1, 1)->addDays($data[3] - 2);
                        @endphp
                        <div><span class="bold">DOB:</span> {{ $dob_date }}</div>
                        <div><span class="bold">Age:</span> {{ $dob->age }}</div>
                    @endif
                    <div><span class="bold">Sex:</span> {{ $data[4] }}</div>
                </td>
                <td style="width: 50%; border: 1px solid #000; padding: 5px; vertical-align: top;">
                    <div>{{ $data[5] }}</div>
                    <div>{{ $data[6] }}, {{ $data[7] }} {{ $data[8] }}</div>
                    <div><span class="bold">Phone:</span> {{ $data[9] }}</div>
                </td>
            </tr>
        </table>

        <!-- Prescription Section -->
        <table class="prescription-table">
            <!-- Drug Row -->
            <tr>
                <td>
                    <div class="prescription-row"><span class="bold">Drug:</span>
                        {{ $data[10] . ' ' . $data[11] . ' ' . $data[12] }}</div>
                </td>
            </tr>
            <!-- Separator Line -->
            <tr>
                <td style="border-top: 1px solid #000; height: 1px; padding: 0;"></td>
            </tr>
            <!-- SIG and other details -->
            <tr>
                <td>
                    <div class="prescription-row"><span class="bold">SIG:</span> {{ $data[15] ?? '' }}</div>
                    <div class="prescription-row"><span class="bold">Dispense:</span> {{ $data[14] ?? '' }}</div>
                    <div class="prescription-row"><span class="bold">Units:</span></div>
                    <div class="prescription-row"><span class="bold">Dispense as Written:</span></div>
                    <div class="prescription-row"><span class="bold">Refills:</span> {{ $data[13] ?? '' }}</div>
                    <div class="prescription-row"><span class="bold">Days Supply:</span></div>
                    <div class="prescription-row"><span class="bold">Notes:</span> {{ $data[16] ?? '' }}</div>
                </td>
            </tr>
        </table>

        <!-- Signature Section -->
        <div>
            <div><span class="bold">Electronically signed by:</span>
                @if (isset($isSigned) && $isSigned)
                    {{ $doctorName ?? '' }}
                @endif
            </div>
            @if (isset($userSignature) && $userSignature)
                <div style="margin-top: 10px; margin-bottom: 10px;">
                    <img src="{{ $userSignature }}" alt="Doctor's Signature"
                        style="max-width: 200px; max-height: 80px;">
                </div>
            @else
                <div class="signature-line"></div>
            @endif
            <div>Substitution permitted</div>

            @if (isset($isSigned) && $isSigned)
                <div>Signed at: {{ $signed_at ?? now()->format('m/d/Y h:i A') }}</div>
                <div>IP Address: {{ $ip_address ?? request()->ip() }}</div>
            @endif
        </div>
    </div>

    @if (!isset($isPdfDownload) || !$isPdfDownload)
        <!-- Download Button (only visible in browser, not in PDF) -->
        <div class="download-button" style="text-align: center; margin-top: 20px;">
            <a href="{{ route('excel.download-pdf') }}"
                style="display: inline-block; background-color: #000000; color: white; padding: 12px 24px; text-decoration: none; font-size: 16px; border-radius: 4px; font-weight: bold;">
                Download PDF
            </a>
        </div>
    @endif

    <style>
        @media print {
            .download-button {
                display: none !important;
            }
        }
    </style>
</body>

</html>
