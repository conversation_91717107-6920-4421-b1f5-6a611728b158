@extends('master')

@section('content')
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="users_dt"></div>
        </div>
    </div>
@endsection
@section('styles')
@endsection
@section('scripts')
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `{{ url('/storage') }}`;
        const apiRoute = `{{ route('archive.api') }}`;
        let url = "{{ Storage::url('/') }}";
        const deleteRoute = `{{ route('users.delete', ['::ID']) }}`;
        const viewRoute = `{{ route('archive.show-all-pdf', ['importId' => '::ID']) }}`;
        const downloadRoute = `{{ route('archive.download-all-pdf', ['importId' => '::ID']) }}`;


        datatableElement = $('#users_dt');
        searchElement = $('#users_search');

        columnArray = [{
                field: 'file_name',
                title: `File Name`,
                width: 400,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'created_at',
                title: `Created At`,
                width: 200,
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
                }
            },
            {
                field: 'Actions',
                title: 'Actions',
                sortable: false,
                width: 'auto',
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `
                            <a data-id="${data.id}" class="btn btn-sm btn-clean btn-icon" id="download-all-btn" data-toggle="tooltip" title="Download All Scripts">
                                <i class="menu-icon fas fa-download"></i>
                            </a>
                            <a href="${viewRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="View Scripts">
                                <i class="menu-icon fas fa-eye"></i>
                            </a>
                            `;
                },
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        const routeTemplate = "{{ route('archive.download-all-pdf', ['importId' => '__ID__']) }}";

        // Handle download all button
        datatableElement.on('click', '#download-all-btn', function() {
            const importId = $(this).data('id');
            const url = routeTemplate.replace('__ID__', importId ?? '');

            const form = $('<form>', {
                method: 'POST',
                action: url
            });

            // Add CSRF token
            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '{{ csrf_token() }}'
            }));

            // Submit the form
            $('body').append(form);
            form.submit();
            form.remove();
        });
    </script>
@endsection
