

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-12 col-sm">
                    <a href="<?php echo e(route('admin.create')); ?>">
                        <button type="button" class="btn btn-primary" id="add-box-btn">
                            <i class="fa fa-plus"></i>
                            <span>Add Staff</span>
                        </button>
                    </a>
                </div>

            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="users_dt"></div>

        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const apiRoute = `<?php echo e(route('admin.api')); ?>`;
        const editRoute = `<?php echo e(route('admin.edit', ['user' => '::ID'])); ?>`;
        const statusChangeRoute = `<?php echo e(route('admin.status', ['user' => '::ID'])); ?>`;
        const deleteRoute = `<?php echo e(route('admin.delete', ['::ID'])); ?>`;
        let url = "<?php echo e(Storage::url('/')); ?>";

        datatableElement = $('#users_dt');
        searchElement = $('#users_search');

        columnArray = [{
                field: 'first_name',
                title: `First Name`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'last_name',
                title: `Last Name`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'email',
                title: `Email`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'role',
                title: `Access Type`,
                width: 200,
                sortable: true,
                autoHide: false,
                template: function(row) {
                    return row.role.charAt(0).toUpperCase() + row.role.slice(1);
                }
            },
            {
                field: 'is_active',
                title: 'active',
                overflow: 'visible',
                autoHide: false,
                width: 70,
                template: function(row) {
                    return `
                        <span class="switch switch-sm switch-primary">
                            <label>
                                <input type="checkbox" data-id="${row.id}" class="status-change" ${row.is_active ? '' : 'checked'} name="${row.id}"/>
                                <span></span>
                            </label>
                        </span>
                    `;
                }
            },
            {
                field: 'Actions',
                title: 'Actions',
                sortable: false,
                width: 'auto',
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `
                            <a href="${editRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" title="Edit details">
                                <i class="menu-icon fas fa-pen"></i>
                                   </a>

                                    <a data-id="${data.id}" onclick="deleteRequest(this)" class="deleteRequest btn btn-sm btn-clean btn-icon" title="Delete">
                                         <i class="menu-icon fas fa-trash"></i>
                                       </a>
                                    `;
                },
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        function deleteRequest(btn) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You want to Delete this User?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Delete the User!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', $(btn).data('id')),
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        success: function(res) {
                            toastr.success(res.message);
                            datatable.reload();
                        }
                    });
                }
            });
        }
        datatableElement.on('click', '.status-change', function() {
            let id = $(this).data('id');
            let val = $(this).is(":checked") ? 1 : 0;
            $.ajax({
                    url: statusChangeRoute.replace('::ID', id),
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                })
                .done(function(res) {
                    toastr.success(res.message);
                    datatable.reload();
                });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/users/admin-index.blade.php ENDPATH**/ ?>