// Script Notifications Handler
document.addEventListener('DOMContentLoaded', function () {
    console.log('Script notifications initialized');

    // Get the current user role from the meta tag
    const userRoleElement = document.querySelector('meta[name="user-role"]');
    const userRole = userRoleElement ? userRoleElement.getAttribute('content') : null;

    console.log('Current user role:', userRole);

    // Track shown notifications to prevent duplicates
    const shownNotifications = {
        scripts: new Set(),
        statusChanges: new Set()
    };

    // Check if Echo is available (Pusher is configured)
    if (typeof window.Echo !== 'undefined') {
        console.log('Echo is available, subscribing to scripts channel');

        // Listen for script.added events on the scripts channel (for all users)
        const scriptsChannel = config('broadcasting.connections.pusher.channels.scripts');
        console.log('Using scripts channel:', scriptsChannel);

        window.Echo.channel(scriptsChannel)
            .listen('.script.added', function (data) {
                console.log('Received script.added event:', data);

                // Only show import notifications to operators and admins
                if (userRole === 'operator' || userRole === 'administrator') {
                    // Check if we've already shown a notification for this script
                    const notificationId = `script_${data.id}`;
                    if (!shownNotifications.scripts.has(notificationId)) {
                        // Add to tracking set
                        shownNotifications.scripts.add(notificationId);
                        // Show notification
                        showNotificationBanner(data);
                    }
                }
            })
            .listen('.script.status.changed', function (data) {
                console.log('Received script.status.changed event:', data);

                // Only show status change notifications to operators
                if (userRole === 'operator') {
                    // Create a unique ID for this status change event
                    const notificationId = `status_${data.user_id}_${data.timestamp}`;
                    if (!shownNotifications.statusChanges.has(notificationId)) {
                        // Add to tracking set
                        shownNotifications.statusChanges.add(notificationId);
                        // Show notification
                        showStatusChangeNotificationBanner(data);
                    }
                }
            });
    } else {
        console.warn('Echo is not available. Real-time notifications will not work.');
    }

    // Function to show notification banner for new scripts
    function showNotificationBanner(data) {
        // Check if notification container exists, if not create it
        let container = document.getElementById('script-notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'script-notification-container';
            container.style.position = 'fixed';
            container.style.top = '20px';
            container.style.right = '20px';
            container.style.width = '350px';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        // Remove all existing notifications to ensure only one is shown at a time
        while (container.firstChild) {
            container.removeChild(container.firstChild);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'script-notification';
        notification.style.backgroundColor = '#dff9ff'; // User's preferred color
        notification.style.borderLeft = '4px solid #36a3f7';
        notification.style.padding = '15px 20px';
        notification.style.margin = '0 0 15px 0';
        notification.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
        notification.style.borderRadius = '5px'; // User's preferred border-radius
        notification.style.display = 'flex';
        notification.style.flexDirection = 'column';
        notification.style.position = 'relative';
        notification.style.transition = 'all 0.3s ease';
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';

        // Trigger animation after a small delay
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Add close icon in the top-right corner
        const closeIcon = document.createElement('div');
        closeIcon.innerHTML = '&times;';
        closeIcon.style.position = 'absolute';
        closeIcon.style.top = '5px';
        closeIcon.style.right = '10px';
        closeIcon.style.fontSize = '20px';
        closeIcon.style.fontWeight = 'bold';
        closeIcon.style.color = '#999';
        closeIcon.style.cursor = 'pointer';
        closeIcon.onclick = function () {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        };

        // Create message content
        const messageContent = document.createElement('div');
        messageContent.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px; font-size: 16px;">New Script Added</div>
            <div style="margin-bottom: 10px;">
                ${data.patient_name ? `Patient: <strong>${data.patient_name}</strong>` : ''}
                ${data.import_id ? `<br>Import ID: <strong>${data.import_id}</strong>` : ''}
            </div>
            <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">${data.timestamp}</div>
        `;

        // Create action buttons
        const actionButtons = document.createElement('div');
        actionButtons.style.marginTop = '15px';
        actionButtons.style.display = 'flex';
        actionButtons.style.justifyContent = 'flex-end';

        // View button
        const viewButton = document.createElement('button');
        viewButton.innerText = 'View';
        viewButton.style.backgroundColor = '#36a3f7';
        viewButton.style.color = 'white';
        viewButton.style.border = 'none';
        viewButton.style.padding = '8px 15px';
        viewButton.style.borderRadius = '3px';
        viewButton.style.cursor = 'pointer';
        viewButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
        viewButton.style.transition = 'all 0.2s ease';
        viewButton.onmouseover = function () {
            viewButton.style.backgroundColor = '#2a8ad6';
        };
        viewButton.onmouseout = function () {
            viewButton.style.backgroundColor = '#36a3f7';
        };
        viewButton.onclick = function () {
            const currentPath = window.location.pathname;
            const isReadyToSendPage = currentPath.includes('/scripts/ready-to-send');

            if (isReadyToSendPage) {
                window.location.reload();
            } else {
                const staffPortalDomain = window.appConfig.staffPortalDomain;
                const redirectPath = '/scripts/ready-to-send/approval';

                if (staffPortalDomain) {
                    // Make sure the domain does not have a trailing slash
                    const cleanDomain = staffPortalDomain.replace(/\/$/, '');
                    window.location.href = `${cleanDomain}${redirectPath}`;
                } else {
                    window.location.href = redirectPath;
                }
            }
        };



        // Add buttons to action container
        actionButtons.appendChild(viewButton);

        // Add content and buttons to notification
        notification.appendChild(closeIcon);
        notification.appendChild(messageContent);
        notification.appendChild(actionButtons);

        // Add notification to container
        container.appendChild(notification);

        // No auto-removal timeout - notification will stay until user closes it
    }

    // Function to show notification banner for status changes
    function showStatusChangeNotificationBanner(data) {
        // Check if notification container exists, if not create it
        let container = document.getElementById('script-notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'script-notification-container';
            container.style.position = 'fixed';
            container.style.top = '20px';
            container.style.right = '20px';
            container.style.width = '350px';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        // Remove all existing notifications to ensure only one is shown at a time
        while (container.firstChild) {
            container.removeChild(container.firstChild);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'script-notification';
        notification.style.backgroundColor = '#dff9ff'; // User's preferred color
        notification.style.borderLeft = '4px solid #36a3f7';
        notification.style.padding = '15px 20px';
        notification.style.margin = '0 0 15px 0';
        notification.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
        notification.style.borderRadius = '5px'; // User's preferred border-radius
        notification.style.display = 'flex';
        notification.style.flexDirection = 'column';
        notification.style.position = 'relative';
        notification.style.transition = 'all 0.3s ease';
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';

        // Trigger animation after a small delay
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Add close icon in the top-right corner
        const closeIcon = document.createElement('div');
        closeIcon.innerHTML = '&times;';
        closeIcon.style.position = 'absolute';
        closeIcon.style.top = '5px';
        closeIcon.style.right = '10px';
        closeIcon.style.fontSize = '20px';
        closeIcon.style.fontWeight = 'bold';
        closeIcon.style.color = '#999';
        closeIcon.style.cursor = 'pointer';
        closeIcon.onclick = function () {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        };

        // Create message content
        const messageContent = document.createElement('div');
        messageContent.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px; font-size: 16px;">Scripts Sent For Approval</div>
            <div style="margin-bottom: 10px;">
                ${data.user_name ? `Provider: <strong>${data.user_name}</strong>` : ''}
                ${data.count ? `<br>Scripts: <strong>${data.count}</strong>` : ''}
                <br>${data.message}
            </div>
            <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">${data.timestamp}</div>
        `;

        // Create action buttons
        const actionButtons = document.createElement('div');
        actionButtons.style.marginTop = '15px';
        actionButtons.style.display = 'flex';
        actionButtons.style.justifyContent = 'flex-end';

        // View button
        const viewButton = document.createElement('button');
        viewButton.innerText = 'View';
        viewButton.style.backgroundColor = '#36a3f7';
        viewButton.style.color = 'white';
        viewButton.style.border = 'none';
        viewButton.style.padding = '8px 15px';
        viewButton.style.borderRadius = '3px';
        viewButton.style.cursor = 'pointer';
        viewButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
        viewButton.style.transition = 'all 0.2s ease';
        viewButton.onmouseover = function () {
            viewButton.style.backgroundColor = '#2a8ad6';
        };
        viewButton.onmouseout = function () {
            viewButton.style.backgroundColor = '#36a3f7';
        };
        viewButton.onclick = function () {
            // Check if we're already on the "Ready to Send" page
            const currentPath = window.location.pathname;
            const isReadyToSendPage = currentPath.includes('/scripts/ready-to-send/approval');

            if (isReadyToSendPage) {
                // Always reload the whole page when already on the "Ready to Send" page
                window.location.reload();
            } else {
                // Get the staff portal domain from config
                const staffPortalDomain = config('app.staff_portal_domain');

                // Use the staff portal domain if available, otherwise use the redirect_url
                if (staffPortalDomain && data.redirect_url) {
                    // Construct the full URL with the staff portal domain
                    const redirectPath = data.redirect_url.startsWith('/') ? data.redirect_url : '/' + data.redirect_url;
                    window.location.href = redirectPath;
                } else {
                    // Fallback to the original redirect_url
                    window.location.href = data.redirect_url;
                }
            }
        };

        // Add buttons to action container
        actionButtons.appendChild(viewButton);

        // Add content and buttons to notification
        notification.appendChild(closeIcon);
        notification.appendChild(messageContent);
        notification.appendChild(actionButtons);

        // Add notification to container
        container.appendChild(notification);

        // No auto-removal timeout - notification will stay until user closes it
    }
});
