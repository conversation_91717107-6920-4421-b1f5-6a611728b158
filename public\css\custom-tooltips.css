/* Custom Purple-Themed Tooltips */

/* Purple theme tooltip styles */
.tooltip.tooltip-purple {
    /* Prevent initial positioning issues */
    opacity: 0;
    visibility: hidden;
    /* Ensure proper initial positioning */
    position: absolute;
    top: -9999px;
    left: -9999px;
    /* Smooth transition */
    transition: opacity 0.1s ease-in-out, visibility 0.1s ease-in-out;
}

.tooltip.tooltip-purple.show {
    opacity: 1;
    visibility: visible;
    /* Reset positioning when shown */
    position: absolute;
    top: auto;
    left: auto;
}

.tooltip.tooltip-purple .tooltip-inner {
    background-color: #e0dfff;
    color: #000;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(149, 97, 226, 0.3);
    border: 1px solid rgba(149, 97, 226, 0.2);
    white-space: normal;
    max-width: 250px;
    text-align: left;
     word-wrap: break-word
}

/* Arrow colors for different positions */
.tooltip.tooltip-purple.bs-tooltip-top .arrow::before {
    border-top-color: #9561e2;
}

.tooltip.tooltip-purple.bs-tooltip-right .arrow::before {
    border-right-color: #9561e2;
}

.tooltip.tooltip-purple.bs-tooltip-bottom .arrow::before {
    border-bottom-color: #9561e2;
}

.tooltip.tooltip-purple.bs-tooltip-left .arrow::before {
    border-left-color: #9561e2;
}

/* Enhanced positioning and visibility */
.tooltip.tooltip-purple {
    /* Ensure tooltips appear above other elements */
    z-index: 9999 !important;
}

/* Hover effect for action buttons to indicate tooltip availability */
.btn-clean.btn-icon:hover {
    background-color: rgba(149, 97, 226, 0.1) !important;
    border-radius: 6px;
    transition: all 0.2s ease;
}

/* Special styling for action buttons in tables */
.datatable .btn-clean.btn-icon {
    position: relative;
    transition: all 0.2s ease;
}

.datatable .btn-clean.btn-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(149, 97, 226, 0.2);
}

/* Additional styling for better tooltip behavior */
.tooltip.tooltip-purple {
    /* Ensure tooltips don't interfere with page interactions */
    pointer-events: none;
}

/* Responsive tooltip adjustments */
@media (max-width: 768px) {
    .tooltip.tooltip-purple .tooltip-inner {
        font-size: 0.8rem;
        padding: 6px 10px;
        max-width: 200px;
    }
}
