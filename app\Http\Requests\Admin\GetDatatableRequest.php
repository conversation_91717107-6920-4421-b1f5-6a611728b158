<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class GetDatatableRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'page' => '',
            'query.search' => '',
            'pagination.page' => 'gt:0',
            'pagination.perpage' => 'gt:0',
            'sort.sort' => 'in:asc,desc',
            'sort.field' => '',
            'signed_date' => '',
            'script_date' => '',
            'sent_date' => '',
            'provider_id' => '',
            'medication_id' => '',
            'query.signed_date' => '',
            'query.script_date' => '',
            'query.sent_date' => '',
            'query.provider_id' => '',
            'query.medication_id' => '',
            // Log filters
            // 'type' => '',
            // 'user_type' => '',
            // 'date_from' => '',
            // 'date_to' => '',
            // 'query.type' => '',
            // 'query.user_type' => '',
            // 'query.date_from' => '',
            // 'query.date_to' => '',
        ];
    }
}
