<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ForgotPasswordRequest;
use App\Mail\ForgotPasswordLinkMail;
use App\Mail\ForgotPasswordMail;
use App\Models\Import;
use App\Models\ImportFile;
use App\Models\User;
use App\Services\LogService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class HomeController extends Controller
{
    public function index()
    {
        $page_title = 'Dashboard';
        $card_title = 'Dashboard';
        $user = Auth::user();
        $readyToSignCount = 0;
        $readyToSendCount = 0;

        if ($user->role === User::ROLE_ADMIN || $user->role === User::ROLE_OPERATOR) {
            $user_count = Import::count();

            // Calculate ready to send count for operators and admins
            if ($user->role === User::ROLE_OPERATOR) {
                $readyToSendCount = ImportFile::whereIn('status', [
                    ImportFile::STATUS_PENDING_DISPATCH,
                    ImportFile::STATUS_PENDING_APPROVAL
                ])->count();
            }
        } else {
            $user_count = Import::where('user_id', $user->id)->count();

            // Calculate ready to sign count for providers
            if ($user->role === User::ROLE_PROVIDER) {
                $readyToSignCount = ImportFile::whereHas('import', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->where('status', ImportFile::STATUS_NEW)->count();
            }
        }

        return view('dashboard', [
            'page_title' => $page_title,
            'card_title' => $card_title,
            'user_count' => $user_count,
            'readyToSignCount' => $readyToSignCount,
            'readyToSendCount' => $readyToSendCount,
        ]);
    }

    public function forgotSendEmail(ForgotPasswordRequest $request)
    {
        // Check if user exists without revealing this information to the client
        $user = User::where('email', $request->email)->first();
        // $user = User::where('email', $request->email)->firstOrFail();


        // Only process password reset if user exists
        if ($user) {
            // Generate a new random password and ensure it's different from the current one
            $attempts = 0;
            $max_attempts = 5;
            $new_password = Str::random(8);

            // Check if the new password is the same as the old one (unlikely but possible)
            while (Hash::check($new_password, $user->password) && $attempts < $max_attempts) {
                $new_password = Str::random(11);
                $attempts++;
            }

            $user->password = bcrypt($new_password);
            $user->password_changed_at = null; // Force password change after reset
            $user->save();

            try {
                Mail::to($user->email)->send(new ForgotPasswordLinkMail($new_password, $user));

                // Log temporary password sent
                LogService::logTempPasswordSent($user);
            } catch (\Exception $e) {
                // Log the error
                Log::error('Failed to send forgot password email', [
                    'email' => $user->email,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Always return the same response regardless of whether the email exists
        // This is for security reasons to prevent email enumeration
        return redirect()->route('forgot-password', ['message_sent' => true]);
    }
}
