<?php

namespace App\Http\Controllers;

use App\Models\ImportFile;
use App\Traits\FaxManager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Webklex\PDFMerger\Facades\PDFMergerFacade as PDFMerger;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Str;

class TestController extends Controller
{
    use FaxManager;


    public function mergePdf(Request $request)
    {
        $import_files = ImportFile::with('import')
            ->where('status', ImportFile::STATUS_PENDING_APPROVAL)
            // ->whereIn('id', $this->import_file_ids)
            ->get();

        $filePaths = [];
        foreach ($import_files as $import_file) {

            $file = $import_file->file_path;
            if (Storage::exists($file)) {
                $filePaths[] = $file;
            }
        }

        $outputDirectory = 'temp/merged_pdfs';

        // try {
        // Initialize the PDF merger
        $oMerger = PDFMerger::init();

        // Create a unique filename for the merged PDF
        $outputFilename = Str::random(20) . '.pdf';

        // Ensure the output directory exists
        if (!Storage::exists($outputDirectory)) {
            Storage::makeDirectory($outputDirectory);
        }

        $outputPath = "{$outputDirectory}/{$outputFilename}";
        $validPdfsCount = 0;

        // Process each file path
        foreach ($filePaths as $filePath) {
            // Normalize the file path to handle mixed slashes and absolute paths

            // $normalizedPath = $this->normalizeStoragePath($filePath);

            $normalizedPath = $filePath;

            // Skip if file doesn't exist
            if (!Storage::exists($normalizedPath)) {
                return "no path" . $normalizedPath;
                continue;
            }

            $normalizedPath = Storage::path($normalizedPath);
            // Get file extension and check if it's a PDF
            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
            if (strtolower($extension) !== 'pdf') {
                return "no extension";
                continue;
            }
            // Get the full path to the file in storage
            $fullPath = $normalizedPath;

            // Add the PDF to the merger
            $oMerger->addPDF($fullPath, 'all');
            $validPdfsCount++;
        }

        // If no valid PDFs were found, return null
        if ($validPdfsCount === 0) {

            return " no pdfs";
        }

        // Merge the PDFs
        $oMerger->merge();

        // Save the merged PDF
        $fullOutputPath = Storage::path($outputPath);
        $oMerger->save($fullOutputPath);
        return $fullOutputPath;

        // Check if the merged PDF was created successfully
        if (!Storage::exists($outputPath)) {
            return null;
        }

        return [
            'success' => true,
            'path' => $outputPath,
            'full_path' => $fullOutputPath,
            'filename' => $outputFilename,
            'merged_count' => $validPdfsCount
        ];
        // } catch (Exception $e) {

        //     Log::error('Failed to merge PDFs', [
        //         'error' => $e->getMessage(),
        //         'file' => $e->getFile(),
        //         'line' => $e->getLine()
        //     ]);
        //     return null;
        // }
    }
    public function test(Request $request)
    {
        // Check if file exists in the request
        if (!$request->hasFile('fax_file')) {
            return response()->json([
                'success' => '0',
                'message' => 'No file uploaded. Please upload a file with the fax_file parameter.'
            ], 400);
        }

        $file = $request->file('fax_file');

        // Call the uploadFiles method with the file - use static method
        $response = FaxManager::uploadFiles($file);

        return response()->json([
            'success' => '1',
            'response' => $response,
        ]);
    }

    public function sendTest(Request $request)
    {
        if (!$request->hasFile('fax_file')) {
            return response()->json([
                'success' => '0',
                'message' => 'No file uploaded. Please upload file(s) with the fax_file parameter.'
            ], 400);
        }

        $filesArray = [];

        $uploadedFiles = $request->file('fax_file');

        // Handle both single and multiple uploads
        if (!is_array($uploadedFiles)) {
            $uploadedFiles = [$uploadedFiles];
        }

        foreach ($uploadedFiles as $file) {
            // Upload each file and collect the path
            $response = FaxManager::uploadFiles($file);
            if (isset($response['path'])) {
                $filesArray[] = $response['path'];
            }
        }

        // Get user_id from request or use default
        $userId = $request->input('user_id', config('fax.user_id'));
        $to = $request->input('to', config('fax.to_number'));
        $from = $request->input('from', config('fax.from_number'));

        // Now $filesArray will have all file paths
        $sendFax = FaxManager::sendFax($to, $filesArray, $from, $userId);

        return response()->json([
            'success' => '1',
            'message' => 'Fax sent successfully',
            'response' => $sendFax,
        ]);
    }
}
