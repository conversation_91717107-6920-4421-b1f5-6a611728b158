<?php

namespace App\Services;

use App\Models\SystemLog;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class LogService
{
    /**
     * Log a user action
     */
    public static function logUserAction(string $type, string $message, array $context = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => $type,
            'message' => $message,
            'context' => $context,
            'user' => $user,
        ]);
    }

    /**
     * Log a system error
     */
    public static function logSystemError(string $message, array $context = [])
    {
        self::createLog([
            'type' => SystemLog::TYPE_SYSTEM_ERROR,
            'message' => $message,
            'context' => $context,
        ]);
    }

    /**
     * Log user login
     */
    public static function logLogin(User $user)
    {
        self::createLog([
            'type' => SystemLog::TYPE_LOGIN,
            'message' => "User logged in",
            'user' => $user,
        ]);
    }

    /**
     * Log user logout
     */
    public static function logLogout(User $user)
    {
        self::createLog([
            'type' => SystemLog::TYPE_LOGOUT,
            'message' => "User logged out",
            'user' => $user,
        ]);
    }

    /**
     * Log script creation
     */
    public static function logScriptCreated(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_CREATED,
            'message' => "Script created for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Log script editing
     */
    public static function logScriptEdited(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_EDITED,
            'message' => "Script edited for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Log script signing
     */
    public static function logScriptSigned(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_SIGNED,
            'message' => "Script signed for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Log script sending
     */
    public static function logScriptSent(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_SENT,
            'message' => "Script sent for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Log provider creation
     */
    public static function logProviderCreated(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_CREATED,
            'message' => "Provider created: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log provider editing
     */
    public static function logProviderEdited(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_EDITED,
            'message' => "Provider edited: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log provider deactivation
     */
    public static function logProviderDeactivated(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_DEACTIVATED,
            'message' => "Provider deactivated: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log provider activation
     */
    public static function logProviderActivated(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_ACTIVATED,
            'message' => "Provider activated: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log staff creation
     */
    public static function logStaffCreated(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_STAFF_CREATED,
            'message' => "Staff created: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email, 'role' => $staff->role],
            'user' => $user,
        ]);
    }

    /**
     * Log staff editing
     */
    public static function logStaffEdited(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_STAFF_EDITED,
            'message' => "Staff edited: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email, 'role' => $staff->role],
            'user' => $user,
        ]);
    }

    /**
     * Log staff deactivation
     */
    public static function logStaffDeactivated(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();
        
        self::createLog([
            'type' => SystemLog::TYPE_STAFF_DEACTIVATED,
            'message' => "Staff deactivated: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email, 'role' => $staff->role],
            'user' => $user,
        ]);
    }

    /**
     * Log staff activation
     */
    public static function logStaffActivated(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_STAFF_ACTIVATED,
            'message' => "Staff activated: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email, 'role' => $staff->role],
            'user' => $user,
        ]);
    }

    /**
     * Log staff deletion
     */
    public static function logStaffDeleted(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_STAFF_DELETED,
            'message' => "Staff deleted: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email],
            'user' => $user,
        ]);
    }

    /**
     * Log password change
     */
    public static function logPasswordChanged(?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_PASSWORD_CHANGED,
            'message' => "Password changed",
            'user' => $user,
        ]);
    }

    /**
     * Log temporary password sent
     */
    public static function logTempPasswordSent(User $targetUser, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_TEMP_PASSWORD_SENT,
            'message' => "Temporary password sent to {$targetUser->first_name} {$targetUser->last_name}",
            'context' => ['target_user_id' => $targetUser->id, 'target_user_email' => $targetUser->email],
            'user' => $user,
        ]);
    }

    /**
     * Log settings update
     */
    public static function logSettingsUpdated(string $settingType, array $context = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SETTINGS_UPDATED,
            'message' => "Settings updated: {$settingType}",
            'context' => $context,
            'user' => $user,
        ]);
    }

    /**
     * Log Excel file import
     */
    public static function logExcelImported(string $fileName, int $recordCount, array $context = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_EXCEL_IMPORTED,
            'message' => "Excel file imported: {$fileName} ({$recordCount} records)",
            'context' => array_merge($context, ['file_name' => $fileName, 'record_count' => $recordCount]),
            'user' => $user,
        ]);
    }

    /**
     * Log script return
     */
    public static function logScriptReturned(array $scriptData, string $reason = '', ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_RETURNED,
            'message' => "Script returned for {$scriptData['patient_name']}" . ($reason ? " - Reason: {$reason}" : ''),
            'context' => array_merge($scriptData, ['return_reason' => $reason]),
            'user' => $user,
        ]);
    }

    /**
     * Log provider deletion
     */
    public static function logProviderDeleted(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_DELETED,
            'message' => "Provider deleted: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log fax number addition
     */
    public static function logFaxAdded(string $faxNumber, string $label, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_FAX_ADDED,
            'message' => "Fax number added: {$faxNumber} ({$label})",
            'context' => ['fax_number' => $faxNumber, 'label' => $label],
            'user' => $user,
        ]);
    }

    /**
     * Log fax number update
     */
    public static function logFaxUpdated(string $faxNumber, string $label, array $changes = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_FAX_UPDATED,
            'message' => "Fax number updated: {$faxNumber} ({$label})",
            'context' => array_merge(['fax_number' => $faxNumber, 'label' => $label], $changes),
            'user' => $user,
        ]);
    }

    /**
     * Log fax number deletion
     */
    public static function logFaxDeleted(string $faxNumber, string $label, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_FAX_DELETED,
            'message' => "Fax number deleted: {$faxNumber} ({$label})",
            'context' => ['fax_number' => $faxNumber, 'label' => $label],
            'user' => $user,
        ]);
    }

    /**
     * Log fax sending
     */
    public static function logFaxSent(array $scriptData, string $faxNumber, array $context = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_FAX_SENT,
            'message' => "Fax sent for {$scriptData['patient_name']} to {$faxNumber}",
            'context' => array_merge($scriptData, ['fax_number' => $faxNumber], $context),
            'user' => $user,
        ]);
    }

    /**
     * Log script download
     */
    public static function logScriptDownloaded(array $scriptData, string $downloadType = 'single', ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_DOWNLOADED,
            'message' => "Script downloaded for {$scriptData['patient_name']} ({$downloadType})",
            'context' => array_merge($scriptData, ['download_type' => $downloadType]),
            'user' => $user,
        ]);
    }

    /**
     * Log script deletion
     */
    public static function logScriptDeleted(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_DELETED,
            'message' => "Script deleted for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Create a log entry
     */
    private static function createLog(array $data)
    {
        $user = $data['user'] ?? null;
        
        SystemLog::create([
            'timestamp' => now(),
            'type' => $data['type'],
            'user_type' => $user ? $user->role : null,
            'username' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'user_id' => $user ? $user->id : null,
            'message' => $data['message'],
            'context' => $data['context'] ?? null,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
        ]);
    }
}
