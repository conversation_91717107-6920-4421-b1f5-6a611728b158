@extends('master')

@php
    use App\Models\ImportFile;
@endphp

@section('content')
    <div class="card card-custom mb-5 p-6">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between mb-4">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-3">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="ready_to_send_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-auto">
                    <button type="button" id="download-all-global-btn" class="btn btn-dark">
                        <i class="fa fa-download mr-1"></i> Download All
                    </button>
                    <button type="button" id="download-selected-global-btn" class="btn btn-dark">
                        <i class="fa fa-download mr-1"></i> Download Selected
                    </button>
                    <button type="button" id="send-all-global-btn" class="btn btn-dark">
                        <i class="fa fa-paper-plane mr-1"></i> Send All
                    </button>
                    <button type="button" id="send-selected-global-btn" class="btn btn-dark">
                        <i class="fa fa-paper-plane mr-1"></i> Send Selected
                    </button>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <label for="signed_date_filter">Signed Date:</label>
                    <div class="input-group date">
                        <input type="date" class="form-control" id="signed_date_filter" max="{{ date('Y-m-d') }}" />
                        <div class="input-group-append">
                            <button class="btn btn-secondary" type="button" id="clear_date_filter">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="provider_filter">Provider:</label>
                    <select class="form-control" id="provider_filter">
                        <option value="">All Providers</option>
                        @foreach ($providers as $provider)
                            <option value="{{ $provider->id }}">{{ $provider->first_name }} {{ $provider->last_name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="medication_filter">Medication:</label>
                    <select class="form-control" id="medication_filter">
                        <option value="">All Medications</option>
                        @foreach ($medications as $medication)
                            <option value="{{ $medication->id }}">{{ $medication->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div style="overflow-x: auto;">
                <div class="datatable datatable-bordered datatable-head-custom" id="ready_to_send_dt"></div>
            </div>

        </div>
    </div>

    <!-- Script Preview Modal -->
    <div class="modal fade" id="scriptPreviewModal" tabindex="-1" role="dialog" aria-labelledby="scriptPreviewModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scriptPreviewModalLabel">Script Preview</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="script-preview-content">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" id="download-preview-btn" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download
                    </a>
                    <button type="button" id="send-preview-btn" class="btn btn-success">
                        <i class="fas fa-paper-plane"></i> Send
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Return Script Modal -->
    <div class="modal fade" id="returnScriptModal" tabindex="-1" role="dialog" aria-labelledby="returnScriptModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="returnScriptModalLabel">Return Script</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="return-reason">Reason for return:</label>
                        <textarea class="form-control" id="return-reason" placeholder="Enter reason..." rows="4"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirm-return-script-btn">Return Script</button>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('styles')
    <style>
        button:disabled {
            cursor: not-allowed !important;
        }

        .text-primary {
            text-decoration: none;
        }

        .text-primary:hover {
            text-decoration: underline;
        }

        /* Custom styles for the preview modal */
        #scriptPreviewModal .modal-dialog {
            max-width: 95%;
            height: 95vh;
            margin: 0.5rem auto;
        }

        #scriptPreviewModal .modal-content {
            height: 100%;
            border-radius: 4px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }

        #scriptPreviewModal .modal-body {
            flex: 1;
            overflow: hidden;
            padding: 0;
        }

        #scriptPreviewModal .modal-header {
            border-bottom: 1px solid #ebedf3;
            padding: 1rem 1.75rem;
        }

        #scriptPreviewModal .modal-footer {
            border-top: 1px solid #ebedf3;
            padding: 1rem 1.75rem;
            position: relative;
            flex-shrink: 0;
            justify-content: flex-end;
            background-color: #fff;
            z-index: 5;
        }

        #scriptPreviewModal .close {
            cursor: pointer;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            margin: 0;
            padding: 0;
        }

        #scriptPreviewModal .close i {
            font-size: 1rem;
        }

        #scriptPreviewModal .close:hover {
            color: #3699FF;
            background-color: #f3f6f9;
            border-radius: 4px;
        }

        #script-preview-content {
            height: 100%;
            width: 100%;
            overflow: hidden;
            position: relative;
        }

        #script-preview-content iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }

        /* Simple style for clickable rows */
        #ready_to_send_dt tbody tr td:not(:last-child) {
            cursor: pointer;
        }

        /* Highlight on hover */
        #ready_to_send_dt tbody tr:hover td:not(:last-child) {
            background-color: rgba(54, 153, 255, 0.1) !important;
        }
    </style>
@endsection
@section('scripts')
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;


        const storagePath = `{{ url('/storage') }}`;
        const apiRoute = `{{ route('scripts.api.ready-to-send') }}`;
        let url = "{{ Storage::url('/') }}";
        const sendAllRoute = `{{ route('scripts.send-fax') }}`;
        const viewRoute = `{{ route('scripts.show-pdf', ['importFile' => '::ID']) }}`;
        const userEditRoute = `{{ route('users.edit', ['user' => '::ID']) }}`;
        const deleteRoute = `{{ route('scripts.delete', ['importFile' => '::ID']) }}`;
        const returnRoute = `{{ route('scripts.api.return-script') }}`;

        datatableElement = $('#ready_to_send_dt');
        searchElement = $('#ready_to_send_search');

        columnArray = [{
                field: 'checkbox',
                title: '<label class="checkbox checkbox-single checkbox-all"><input type="checkbox" id="select-all-checkbox" />&nbsp;<span></span></label>',
                sortable: false,
                width: 100,
                autoHide: false,
                textAlign: 'center',
                template: function(data) {
                    // Check if this item is in the globalSelectedIds array
                    const isChecked = data.is_selected || globalSelectedIds.includes(data.id.toString()) ?
                        'checked' : '';
                    return `<label class="checkbox checkbox-single">
                    <input type="checkbox" class="row-checkbox" value="${data.id}" ${isChecked} />&nbsp;<span></span>
                </label>`;
                }
            },
            {
                field: 'import_file_name',
                title: `File Name`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return `<div style="white-space: normal; text-wrap: wrap;">${data.import_file_name ?? ''}</div>`;
                }
            },
            {
                field: 'created_at',
                title: `Created At`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
                }
            },
            {
                field: 'signed_at',
                title: `Signed at`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.signed_at ? moment.parseZone(data.signed_at).format('MM/DD/YYYY hh:mm A') :
                        '<b>Not Signed Yet</b>';
                }
            },
            {
                field: 'sent_at',
                title: `Sent at`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.sent_at ? moment(data.sent_at).format('MM/DD/YYYY hh:mm A') :
                        '<b>Not Sent Yet</b>';
                }
            },
            {
                field: 'script_date',
                title: `Script date`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return moment(data.script_date).format('MM/DD/YYYY');
                }
            },
            {
                field: 'last_name',
                title: `Last name`,
                width: 'auto',
                sortable: true,
                autoHide: false
            },
            {
                field: 'first_name',
                title: `First name`,
                width: 'auto',
                sortable: true,
                autoHide: false
            },
            {
                field: 'medication',
                title: `Medication`,
                width: 'auto',
                sortable: true,
                autoHide: false
            },
            {
                field: 'provider_name',
                title: `Provider Name`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    if (data.import && data.import.user && data.import.user.id) {
                        return `<a href="${userEditRoute.replace('::ID', data.import.user.id)}" class="text-primary font-weight-bold" title="Edit Provider">
                ${data.provider_name}
            </a>`;
                    } else {
                        return data.provider_name || '';
                    }
                }
            },
            {
                field: 'status',
                title: `Status`,
                width: 'auto',
                sortable: true,
                autoHide: false
            },
            {
                field: 'Actions',
                title: 'Actions',
                width: 'auto',
                sortable: false,
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    const downloadRoute = `{{ route('archive.file-download', ['id' => '::ID']) }}`.replace('::ID',
                        data.id);
                    const viewUrl = viewRoute.replace('::ID', data.id);
                    const faxRoute = `{{ route('archive.send-fax', ['id' => '::ID']) }}`.replace('::ID', data.id);

                    const viewBtn = `
                            <a href="#" data-id="${data.id}" data-view-route="${viewUrl}" data-download-route="${downloadRoute}" data-send-route="${sendAllRoute}" class="btn btn-sm btn-clean btn-icon preview-btn" data-toggle="tooltip" title="Preview Script">
                                <i class="menu-icon fas fa-eye"></i>
                            </a>`;
                    const downloadBtn = `
                            <a href="${downloadRoute}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Download Script">
                                <i class="menu-icon fas fa-download"></i>
                            </a>`;
                    // Only show fax button for files with Pending Approval status
                    let faxBtn = '';
                    if (data.status === '{{ ImportFile::STATUS_PENDING_APPROVAL }}') {
                        faxBtn = `
                            <a href="${faxRoute}" class="btn btn-sm btn-clean btn-icon send-fax-btn" data-toggle="tooltip" title="Send Fax">
                                <i class="menu-icon fa fa-paper-plane"></i>
                            </a>`;
                    }

                    // Delete button - only for administrators and scripts with status 'New' or 'Pending Approval'
                    const userRole = '{{ Auth::user()->role }}';
                    const allowedStatuses = ['{{ ImportFile::STATUS_NEW }}',
                        '{{ ImportFile::STATUS_PENDING_APPROVAL }}'
                    ];
                    const deleteBtn = (userRole === '{{ \App\Models\User::ROLE_ADMIN }}' && allowedStatuses
                        .includes(data.status)) ? `
                            <a href="#" data-id="${data.id}" class="btn btn-sm btn-clean btn-icon delete-script-btn" data-toggle="tooltip" title="Delete Script">
                                <i class="menu-icon fas fa-trash"></i>
                            </a>` : '';

                    const returnBtn = `
                            <a href="#" data-id="${data.id}" data-return-route="${returnRoute}" class="btn btn-sm btn-clean btn-icon return-btn" data-toggle="tooltip" title="Return Script">
                                <span class="menu-icon"><i class="fas fa-share"></i></span>
                                </a>`;

                    return downloadBtn + viewBtn + faxBtn + deleteBtn + returnBtn;
                }
            }

        ];

        // datatable = datatableElement.KTDatatable({
        //     data: {
        //         type: 'remote',
        //         source: {
        //             read: {
        //                 url: apiRoute,
        //                 //sample custom headers
        //                 headers: {
        //                     'X-CSRF-TOKEN': '{{ csrf_token() }}'
        //                 },
        //                 map: function(raw) {
        //                     // sample data mapping
        //                     var dataSet = raw;
        //                     if (typeof raw.data !== 'undefined') {
        //                         dataSet = raw.data;
        //                     }
        //                     return dataSet;
        //                 },
        //             },
        //         },
        //         pageSize: 10,
        //         serverPaging: true,
        //         serverFiltering: true,
        //         serverSorting: true,
        //     },
        //     layout: {
        //         scroll: false,
        //         footer: false
        //     },
        //     sortable: true,
        //     pagination: true,
        //     search: {
        //         input: searchElement,
        //         key: 'search'
        //     },
        //     columns: columnArray
        // });
        // Store selected IDs globally
        let globalSelectedIds = [];

        // Initialize datatable with empty query parameters
        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        params: function() {
                            // Get the current query parameters
                            const query = datatable.getDataSourceQuery();

                            // Get the current search value directly from the search input
                            const searchValue = $(searchElement).val() || '';

                            // Add selected IDs to the request
                            return {
                                displayed_ids: globalSelectedIds,
                                signed_date: query.signed_date || '',
                                provider_id: query.provider_id || '',
                                medication_id: query.medication_id || '',
                                search: searchValue,
                                query: {
                                    signed_date: query.signed_date || '',
                                    provider_id: query.provider_id || '',
                                    medication_id: query.medication_id || '',
                                    search: searchValue
                                }
                            };
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;

                                // Store the selected IDs from the response if available
                                if (raw.meta && raw.meta.selectedIds) {
                                    globalSelectedIds = raw.meta.selectedIds;
                                }

                                // Mark items as selected based on is_selected property
                                dataSet.forEach(function(item) {
                                    if (item.is_selected) {
                                        // Ensure this ID is in our global selected IDs
                                        if (!globalSelectedIds.includes(item.id)) {
                                            globalSelectedIds.push(item.id);
                                        }
                                    }
                                });
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search',
                delay: 500
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        // Function to disable/enable all buttons on the page
        function togglePageButtons(disable) {
            // Disable/enable only the "All" buttons
            $('#download-all-global-btn').prop('disabled', disable);
            $('#send-all-global-btn').prop('disabled', disable);

            // If disabling all buttons due to empty data, also disable the "Selected" buttons
            if (disable) {
                toggleSelectedButtons(true);
            }
        }

        // Function to toggle the "Selected" buttons based on whether any items are selected
        function toggleSelectedButtons(disable) {
            $('#download-selected-global-btn').prop('disabled', disable);
            $('#send-selected-global-btn').prop('disabled', disable);
        }

        // Function to toggle "All" buttons when items are selected
        function toggleAllButtonsWhenSelected(hasSelection) {
            // When items are selected, disable "All" buttons
            $('#download-all-global-btn').prop('disabled', hasSelection);
            $('#send-all-global-btn').prop('disabled', hasSelection);
        }

        // Initialize the datatable query parameters with empty values
        datatable.setDataSourceQuery({
            query: {
                signed_date: '',
                provider_id: '',
                medication_id: '',
                search: ''
            }
        });

        // Initialize the "Selected" buttons as disabled by default
        toggleSelectedButtons(true);

        // Initialize the "All" buttons as enabled by default (since no selections)
        toggleAllButtonsWhenSelected(false);

        // Handle ajax done event
        datatable.on('datatable-on-ajax-done', function(e, data) {
            // Check if data is empty
            const isEmpty = !data || !data.length;

            // Update the "Select All" checkbox state and button states after data is loaded
            setTimeout(function() {
                // Restore checkboxes for items that were previously selected
                restoreSelectedCheckboxes();

                // Update the "Select All" checkbox state
                updateSelectAllCheckboxState();

                // Update button states based on selection and data availability
                if (isEmpty) {
                    // If no data, disable all buttons
                    togglePageButtons(true);
                } else {
                    // If we have data, enable "All" buttons unless items are selected
                    togglePageButtons(false);
                    toggleSelectedButtons(globalSelectedIds.length === 0);
                    toggleAllButtonsWhenSelected(globalSelectedIds.length > 0);
                }
            }, 100);
        });

        // Handle ajax fail event
        datatable.on('datatable-on-ajax-fail', function(e, jqXHR) {
            // Disable buttons on error
            togglePageButtons(true);
        });

        const routeTemplate = "{{ route('scripts.download-all-pdf') }}";

        $('#download-all-global-btn').on('click', function() {
            const form = $('<form>', {
                method: 'POST',
                action: routeTemplate
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '{{ csrf_token() }}'
            }));

            form.append($('<input>', {
                type: 'hidden',
                name: 'status[]',
                value: '{{ ImportFile::STATUS_PENDING_APPROVAL }}' // <-- Add this to send status in request
            }));
            // Get current filter values and add them to the form
            const providerId = $('#provider_filter').val();
            if (providerId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'provider_id',
                    value: providerId
                }));
            }

            const medicationId = $('#medication_filter').val();
            if (medicationId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'medication_id',
                    value: medicationId
                }));
            }

            const signedDate = $('#signed_date_filter').val();
            if (signedDate) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'signed_date',
                    value: signedDate
                }));
            }

            // Get current search value
            const searchValue = $(searchElement).val();
            if (searchValue) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'search',
                    value: searchValue
                }));
            }

            $('body').append(form);
            form.submit();
            form.remove();
        });

        $('#download-selected-global-btn').on('click', function() {
            const selectedIds = getSelectedIds();

            if (selectedIds.length === 0) {
                return;
            }

            const form = $('<form>', {
                method: 'POST',
                action: routeTemplate.replace('__ID__', '') // same endpoint
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '{{ csrf_token() }}'
            }));

            // Optional: pass status filter if needed
            form.append($('<input>', {
                type: 'hidden',
                name: 'status[]',
                value: '{{ ImportFile::STATUS_PENDING_APPROVAL }}' // change dynamically if needed
            }));

            // Add displayed_ids[] inputs
            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'displayed_ids[]',
                    value: id
                }));
            });

            $('body').append(form);
            form.submit();
            form.remove();
        });

        $('#send-all-global-btn').on('click', function() {
            const form = $('<form>', {
                method: 'POST',
                action: sendAllRoute
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '{{ csrf_token() }}'
            }));

            // Pass status
            form.append($('<input>', {
                type: 'hidden',
                name: 'status',
                value: '{{ ImportFile::STATUS_PENDING_APPROVAL }}' // dynamically set if needed
            }));

            // Pass status
            form.append($('<input>', {
                type: 'hidden',
                name: 'changed_status',
                value: '{{ ImportFile::STATUS_SENT }}' // dynamically set if needed
            }));

            // Get current filter values and add them to the form
            const providerId = $('#provider_filter').val();
            console.log('Selected Provider ID:', providerId);
            if (providerId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'provider_id',
                    value: providerId
                }));
            }

            const medicationId = $('#medication_filter').val();
            if (medicationId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'medication_id',
                    value: medicationId
                }));
            }

            const signedDate = $('#signed_date_filter').val();
            if (signedDate) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'signed_date',
                    value: signedDate
                }));
            }

            // Get current search value
            const searchValue = $(searchElement).val();
            if (searchValue) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'search',
                    value: searchValue
                }));
            }

            $('body').append(form);

            form.submit();
            form.remove();

        });

        $('#send-selected-global-btn').on('click', function() {
            const selectedIds = getSelectedIds();

            if (selectedIds.length === 0) {
                return;
            }


            const form = $('<form>', {
                method: 'POST',
                action: sendAllRoute
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '{{ csrf_token() }}'
            }));

            // Optional: pass status filter if needed
            form.append($('<input>', {
                type: 'hidden',
                name: 'status',
                value: '{{ ImportFile::STATUS_PENDING_APPROVAL }}' // change dynamically if needed
            }));

            // Optional: pass status filter if needed
            form.append($('<input>', {
                type: 'hidden',
                name: 'changed_status',
                value: '{{ ImportFile::STATUS_SENT }}' // change dynamically if needed
            }));

            // Add displayed_ids[] inputs
            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'displayed_ids[]',
                    value: id
                }));
            });

            // Get current filter values and add them to the form
            const providerId = $('#provider_filter').val();
            if (providerId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'provider_id',
                    value: providerId
                }));
            }

            const medicationId = $('#medication_filter').val();
            if (medicationId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'medication_id',
                    value: medicationId
                }));
            }

            const signedDate = $('#signed_date_filter').val();
            if (signedDate) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'signed_date',
                    value: signedDate
                }));
            }

            // Get current search value
            const searchValue = $(searchElement).val();
            if (searchValue) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'search',
                    value: searchValue
                }));
            }

            $('body').append(form);

            form.submit();
            form.remove();

        });

        // Handle "Select All" checkbox
        $(document).on('change', '#select-all-checkbox', function() {
            let isChecked = $(this).is(':checked');

            // Check/uncheck all visible checkboxes
            $('.row-checkbox').prop('checked', isChecked);

            // Use our improved getSelectedIds function to update the global selection state
            getSelectedIds();

            // Update button states based on selection
            if (isChecked && $('.row-checkbox').length > 0) {
                toggleAllButtonsWhenSelected(true);
                toggleSelectedButtons(false);
            } else if (!isChecked) {
                // If there are still selected items on other pages
                if (globalSelectedIds.length > 0) {
                    toggleAllButtonsWhenSelected(true);
                    toggleSelectedButtons(false);
                } else {
                    // If nothing is selected anywhere
                    toggleAllButtonsWhenSelected(false);
                    toggleSelectedButtons(true);
                }
            }
        });

        // Handle individual checkbox changes
        $(document).on('change', '.row-checkbox', function() {
            // Use our improved getSelectedIds function to update the global selection state
            getSelectedIds();

            // Update "Select All" checkbox state
            updateSelectAllCheckboxState();
        });

        function getSelectedIds() {
            // Get all visible checkbox IDs on the current page
            let visibleIds = [];
            $('.row-checkbox').each(function() {
                visibleIds.push($(this).val());
            });

            // First, remove any IDs from globalSelectedIds that are visible on the current page
            // This ensures we don't have stale selections
            globalSelectedIds = globalSelectedIds.filter(function(id) {
                return !visibleIds.includes(id);
            });

            // Now add all currently checked IDs to globalSelectedIds
            $('.row-checkbox:checked').each(function() {
                const id = $(this).val();
                globalSelectedIds.push(id);
            });

            // Update the "Select All" checkbox state
            updateSelectAllCheckboxState();

            // Update the "Selected" buttons state - enable when items are selected
            toggleSelectedButtons(globalSelectedIds.length === 0);

            // Update the "All" buttons state - disable when items are selected
            toggleAllButtonsWhenSelected(globalSelectedIds.length > 0);

            return globalSelectedIds;
        }

        // Function to update the "Select All" checkbox state
        function updateSelectAllCheckboxState() {
            const totalCheckboxes = $('.row-checkbox').length;
            const checkedCheckboxes = $('.row-checkbox:checked').length;

            if (totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes) {
                $('#select-all-checkbox').prop('checked', true);
            } else {
                $('#select-all-checkbox').prop('checked', false);
            }
        }

        // Function to restore selected checkboxes based on globalSelectedIds
        function restoreSelectedCheckboxes() {
            // For each checkbox on the current page
            $('.row-checkbox').each(function() {
                const id = $(this).val();
                // If this ID is in our globalSelectedIds array, check it
                if (globalSelectedIds.includes(id)) {
                    $(this).prop('checked', true);
                }
            });
        }

        // Handle pagination events to maintain selection state
        datatable.on('datatable-on-layout-updated', function() {
            // After layout update (which happens on page change), restore selections
            setTimeout(function() {
                restoreSelectedCheckboxes();
                updateSelectAllCheckboxState();
            }, 100);
        });

        // Handle datatable reloads
        datatable.on('datatable-on-reloaded', function() {
            // After reload, restore selections
            setTimeout(function() {
                restoreSelectedCheckboxes();
                updateSelectAllCheckboxState();
            }, 100);
        });

        // Handle page change events
        datatable.on('datatable-on-goto-page', function(e, meta) {
            // When changing pages, we need to preserve our globalSelectedIds
            // The datatable will reload data, and our ajax-done handler will restore selections
            console.log('Page changed to:', meta.page);
        });

        // Special handler for signed date filter to clear selections
        $('#signed_date_filter').on('change', function() {
            // Clear all selected rows when signed date filter changes
            globalSelectedIds = [];

            // Get current filter values
            const signedDate = $(this).val();
            const providerId = $('#provider_filter').val();
            const medicationId = $('#medication_filter').val();

            // Get current search value - this will preserve the search text when changing filters
            const searchValue = $(searchElement).val() || '';

            console.log('Signed date filter values:', {
                signedDate,
                providerId,
                medicationId,
                searchValue
            });

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                signed_date: signedDate,
                provider_id: providerId,
                medication_id: medicationId,
                search: searchValue,
                query: {
                    signed_date: signedDate,
                    provider_id: providerId,
                    medication_id: medicationId,
                    search: searchValue
                }
            });

            // Update button states
            toggleSelectedButtons(true);
            toggleAllButtonsWhenSelected(false);

            // Reload the datatable with the new query parameters
            datatable.reload();
        });

        // Special handler for medication filter to clear selections
        $('#medication_filter').on('change', function() {
            // Clear all selected rows when medication filter changes
            globalSelectedIds = [];

            // Get current filter values
            const signedDate = $('#signed_date_filter').val();
            const providerId = $('#provider_filter').val();
            const medicationId = $(this).val();

            // Get current search value - this will preserve the search text when changing filters
            const searchValue = $(searchElement).val() || '';

            console.log('Medication filter values:', {
                signedDate,
                providerId,
                medicationId,
                searchValue
            });

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                signed_date: signedDate,
                provider_id: providerId,
                medication_id: medicationId,
                search: searchValue,
                query: {
                    signed_date: signedDate,
                    provider_id: providerId,
                    medication_id: medicationId,
                    search: searchValue
                }
            });

            // Update button states
            toggleSelectedButtons(true);
            toggleAllButtonsWhenSelected(false);

            // Reload the datatable with the new query parameters
            datatable.reload();
        });

        // Special handler for provider filter to clear selections
        $('#provider_filter').on('change', function() {
            // Clear all selected rows when provider filter changes
            globalSelectedIds = [];

            // Get current filter values
            const signedDate = $('#signed_date_filter').val();
            const providerId = $(this).val();
            const medicationId = $('#medication_filter').val();

            // Get current search value - this will preserve the search text when changing filters
            const searchValue = $(searchElement).val() || '';

            console.log('Provider filter values:', {
                signedDate,
                providerId,
                medicationId,
                searchValue
            });

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                signed_date: signedDate,
                provider_id: providerId,
                medication_id: medicationId,
                search: searchValue,
                query: {
                    signed_date: signedDate,
                    provider_id: providerId,
                    medication_id: medicationId,
                    search: searchValue
                }
            });

            // Update button states
            toggleSelectedButtons(true);
            toggleAllButtonsWhenSelected(false);

            // Reload the datatable with the new query parameters
            datatable.reload();
        });

        // Clear date filter
        $('#clear_date_filter').on('click', function() {
            $('#signed_date_filter').val('');

            // Clear all selected rows when date filter is cleared
            globalSelectedIds = [];

            // Update the query parameters
            const providerId = $('#provider_filter').val();
            const medicationId = $('#medication_filter').val();

            // Get current search value - this will preserve the search text when clearing date filter
            const searchValue = $(searchElement).val() || '';

            console.log('Clear date filter values:', {
                providerId,
                medicationId,
                searchValue
            });

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                signed_date: '',
                provider_id: providerId,
                medication_id: medicationId,
                search: searchValue,
                query: {
                    signed_date: '',
                    provider_id: providerId,
                    medication_id: medicationId,
                    search: searchValue
                }
            });

            // Update button states
            toggleSelectedButtons(true);
            toggleAllButtonsWhenSelected(false);

            // Reload the datatable
            datatable.reload();
        });

        // Add event listener for preview buttons
        $(document).on('click', '.preview-btn', function(e) {
            e.preventDefault();

            const fileId = $(this).data('id');
            const viewRoute = $(this).data('view-route');
            const downloadRoute = $(this).data('download-route');
            const sendRoute = $(this).data('send-route');

            // Set the download button URL
            $('#download-preview-btn').attr('href', downloadRoute);

            // Store the file ID for the send button
            $('#send-preview-btn').data('file-id', fileId);

            // Instead of trying to get the row data from the datatable record,
            // let's find the row data directly from the datatable source
            let rowData = null;
            const allData = datatable.getDataSourceParam('data');

            // Find the row with matching ID
            if (allData && allData.length) {
                rowData = allData.find(item => item.id == fileId);
            }

            // If we couldn't find the row data, try an alternative approach
            if (!rowData) {
                // Try to get all rows from the current page
                const rows = datatable.getColumn('status').cells;

                // Get the status directly from the DOM if possible
                const statusCell = $(this).closest('tr').find(
                    'td:contains("{{ ImportFile::STATUS_PENDING_APPROVAL }}")');

                // Default to enabling the button for now
                $('#send-preview-btn').removeClass('disabled').prop('disabled', false);
            } else {
                // Enable the send button if status is "{{ ImportFile::STATUS_PENDING_APPROVAL }}"
                if (rowData.status === '{{ ImportFile::STATUS_PENDING_APPROVAL }}') {
                    $('#send-preview-btn').removeClass('disabled').prop('disabled', false);
                } else {
                    $('#send-preview-btn').addClass('disabled').prop('disabled', true)
                        .attr('title', 'Cannot send at this stage');
                }
            }

            // Show the modal
            $('#scriptPreviewModal').modal('show');

            // Load the script preview
            $('#script-preview-content').html(
                '<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>'
            );

            // Load the PDF in an iframe
            setTimeout(function() {
                $('#script-preview-content').html(
                    `<iframe src="${viewRoute}" width="100%" height="100%" style="height: 90vh;" frameborder="0"></iframe>`
                );
            }, 500);
        });

        // Handle send button click in the modal
        $('#send-preview-btn').on('click', function() {
            const fileId = $(this).data('file-id');

            if (!fileId) {
                console.error('No file ID found for sending');
                return;
            }

            // Create a form for POST submission
            const form = $('<form>', {
                method: 'POST',
                action: sendAllRoute
            });

            // Add CSRF token
            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '{{ csrf_token() }}'
            }));

            // Add status parameter
            form.append($('<input>', {
                type: 'hidden',
                name: 'status',
                value: '{{ ImportFile::STATUS_PENDING_APPROVAL }}'
            }));

            // Add changed_status parameter
            form.append($('<input>', {
                type: 'hidden',
                name: 'changed_status',
                value: '{{ ImportFile::STATUS_SENT }}'
            }));

            // Add the file ID to the displayed_ids array
            form.append($('<input>', {
                type: 'hidden',
                name: 'displayed_ids[]',
                value: fileId
            }));

            // Get current filter values and add them to the form
            const providerId = $('#provider_filter').val();
            if (providerId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'provider_id',
                    value: providerId
                }));
            }

            const medicationId = $('#medication_filter').val();
            if (medicationId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'medication_id',
                    value: medicationId
                }));
            }

            const signedDate = $('#signed_date_filter').val();
            if (signedDate) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'signed_date',
                    value: signedDate
                }));
            }

            // Append the form to the body, submit it, and then remove it
            $('body').append(form);
            form.submit();
            form.remove();
        });

        // Handle modal events
        $('#scriptPreviewModal').on('hidden.bs.modal', function() {
            // Clear the preview content when modal is closed
            $('#script-preview-content').html('');
        });

        // Simple row click handler
        $(document).on('click', '#ready_to_send_dt tbody tr td', function(e) {
            // Skip if clicking on the checkbox cell or actions cell
            if ($(this).is(':first-child') || $(this).is(':last-child') ||
                $(e.target).is('input[type="checkbox"]') ||
                $(e.target).closest('a').length ||
                $(e.target).closest('button').length ||
                $(e.target).closest('i').length) {
                return;
            }

            // Find the checkbox in the first cell
            const checkbox = $(this).closest('tr').find('td:first-child input[type="checkbox"]');

            // Toggle the checkbox
            checkbox.prop('checked', !checkbox.prop('checked'));

            // Trigger change event
            checkbox.trigger('change');
        });

        // Handle delete script button click
        $(document).on('click', '.delete-script-btn', function(e) {
            e.preventDefault();

            const scriptId = $(this).data('id');

            Swal.fire({
                title: 'Are you sure?',
                text: "Are you sure you want to delete this script?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes',
                cancelButtonText: 'No'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', scriptId),
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        success: function(res) {
                            if (res.status === '1') {
                                toastr.success(res.message);
                                window.location.reload();
                            } else {
                                toastr.error(res.message);
                            }
                        },
                        error: function(xhr) {
                            let message = 'Error deleting script';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                message = xhr.responseJSON.message;
                            }
                            toastr.error(message);
                        }
                    });
                }
            });
        });

        let returnScriptId = null;
        $(document).on('click', '.return-btn', function(e) {
            e.preventDefault();
            returnScriptId = $(this).data('id');
            $('#return-reason').val('');
            $('#returnScriptModal').modal('show');
        });

        $('#confirm-return-script-btn').on('click', function() {
            const reason = $('#return-reason').val();
            if (!reason) {
                toastr.error('Please enter a reason for return.');
                return;
            }
            if (!returnScriptId) {
                toastr.error('No script selected.');
                return;
            }
            $.ajax({
                url: "{{ route('scripts.api.return-script') }}",
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    id: returnScriptId,
                    reason: reason
                },
                success: function(res) {
                    toastr.success(res.message || 'Script returned successfully.');
                    $('#returnScriptModal').modal('hide');
                    datatable.reload();
                },
                error: function(xhr) {
                    // console.log(data);
                    let message = 'Failed to return script.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    toastr.error(message);
                }
            });
        });
    </script>
@endsection
