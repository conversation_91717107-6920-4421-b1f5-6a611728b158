@extends('master')

@section('content')
    @php
        use App\Models\User;
    @endphp

    @if (Auth::user()->role === User::ROLE_ADMIN)
        <div class="row">
            <div class="col-12 col-md-4 col-lg-2">
                <a href="{{ route('users.create') }}" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-plus icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Add Provider</h4>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-12 col-md-4 col-lg-2">
                <a href="{{ route('excel.staff-bulk-import') }}" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-file-import icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Bulk Import</h4>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    @elseif (Auth::user()->role === User::ROLE_OPERATOR)
        <div class="row">
            <div class="col-12 col-md-4 col-lg-2">
                <a href="{{ route('users.create') }}" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-plus icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Add Provider</h4>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-12 col-md-4 col-lg-2">
                <a href="{{ route('excel.staff-bulk-import') }}" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-file-import icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Bulk Import</h4>
                        </div>
                    </div>
                </a>
            </div>

            @if ($readyToSendCount > 0)
                <div class="col-12 col-md-4 col-lg-2">
                    <a href="{{ route('scripts.ready-to-send') }}" class="text-decoration-none">
                        <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                            <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                                <i class="fa fa-paper-plane icon-3x mb-3"></i>
                                <h4 class="font-weight-bold">Send Scripts</h4>
                            </div>
                        </div>
                    </a>
                </div>
            @endif
        </div>
    @elseif (Auth::user()->role === User::ROLE_PROVIDER)
        <div class="row">
            <div class="col-12 col-md-4 col-lg-2">
                <a href="{{ route('excel.store') }}" class="text-decoration-none">
                    <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                        <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                            <i class="fa fa-file-import icon-3x mb-3"></i>
                            <h4 class="font-weight-bold">Bulk Import</h4>
                        </div>
                    </div>
                </a>
            </div>


            @if ($readyToSignCount > 0)
                <div class="col-12 col-md-4 col-lg-2">
                    <a href="{{ route('scripts.ready-to-sign') }}" class="text-decoration-none">
                        <div class="card card-custom card-stretch gutter-b bg-primary h-100">
                            <div class="card-body text-white d-flex justify-content-center align-items-center flex-column">
                                <i class="fas fa-pen-nib icon-3x mb-3"></i>
                                <h4 class="font-weight-bold">Sign Scripts</h4>
                            </div>
                        </div>
                    </a>
                </div>
            @endif
        </div>
    @endif

    <!-- <x-dashboard.card title="Imports" value="{{ $user_count }}" icon="fa fa-users"  /> -->
@endsection
