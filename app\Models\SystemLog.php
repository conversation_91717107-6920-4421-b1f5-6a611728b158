<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemLog extends Model
{
    use HasFactory;

    // Log types
    const TYPE_SYSTEM_ERROR = 'system_error';
    const TYPE_USER_ACTION = 'user_action';
    const TYPE_LOGIN = 'login';
    const TYPE_LOGOUT = 'logout';
    const TYPE_SCRIPT_CREATED = 'script_created';
    const TYPE_SCRIPT_EDITED = 'script_edited';
    const TYPE_SCRIPT_SIGNED = 'script_signed';
    const TYPE_SCRIPT_SENT = 'script_sent';
    const TYPE_PROVIDER_CREATED = 'provider_created';
    const TYPE_PROVIDER_EDITED = 'provider_edited';
    const TYPE_PROVIDER_DEACTIVATED = 'provider_deactivated';
    const TYPE_PROVIDER_ACTIVATED = 'provider_activated';
    const TYPE_STAFF_CREATED = 'staff_created';
    const TYPE_STAFF_EDITED = 'staff_edited';
    const TYPE_STAFF_DEACTIVATED = 'staff_deactivated';
    const TYPE_STAFF_ACTIVATED = 'staff_activated';
    const TYPE_STAFF_DELETED = 'staff_deleted';
    const TYPE_PASSWORD_CHANGED = 'password_changed';
    const TYPE_TEMP_PASSWORD_SENT = 'temp_password_sent';
    const TYPE_SETTINGS_UPDATED = 'settings_updated';
    const TYPE_EXCEL_IMPORTED = 'excel_imported';
    const TYPE_SCRIPT_RETURNED = 'script_returned';
    const TYPE_PROVIDER_DELETED = 'provider_deleted';
    const TYPE_FAX_ADDED = 'fax_added';
    const TYPE_FAX_UPDATED = 'fax_updated';
    const TYPE_FAX_DELETED = 'fax_deleted';
    const TYPE_FAX_SENT = 'fax_sent';
    const TYPE_SCRIPT_DOWNLOADED = 'script_downloaded';
    const TYPE_SCRIPT_DELETED = 'script_deleted';

    protected $table = 'system_logs';

    protected $fillable = [
        'timestamp',
        'type',
        'user_type',
        'username',
        'user_id',
        'message',
        'context',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'timestamp' => 'datetime',
        'context' => 'array',
    ];

    protected $appends = ['type_label', 'user_type_label', 'formatted_timestamp'];

    /**
     * Relationship with User
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get formatted timestamp
     */
    public function getFormattedTimestampAttribute()
    {
        return $this->timestamp->format('m/d/Y, h:i A');
    }

    /**
     * Get type label for display
     */
    public function getTypeLabelAttribute()
    {
        $labels = [
            self::TYPE_SYSTEM_ERROR => 'System Error',
            self::TYPE_USER_ACTION => 'User Action',
            self::TYPE_LOGIN => 'Login',
            self::TYPE_LOGOUT => 'Logout',
            self::TYPE_SCRIPT_CREATED => 'Script Created',
            self::TYPE_SCRIPT_EDITED => 'Script Edited',
            self::TYPE_SCRIPT_SIGNED => 'Script Signed',
            self::TYPE_SCRIPT_SENT => 'Script Sent',
            self::TYPE_PROVIDER_CREATED => 'Provider Created',
            self::TYPE_PROVIDER_EDITED => 'Provider Edited',
            self::TYPE_PROVIDER_DEACTIVATED => 'Provider Deactivated',
            self::TYPE_PROVIDER_ACTIVATED => 'Provider Activated',
            self::TYPE_STAFF_CREATED => 'Staff Created',
            self::TYPE_STAFF_EDITED => 'Staff Edited',
            self::TYPE_STAFF_DEACTIVATED => 'Staff Deactivated',
            self::TYPE_STAFF_ACTIVATED => 'Staff Activated',
            self::TYPE_STAFF_DELETED => 'Staff Deleted',
            self::TYPE_PASSWORD_CHANGED => 'Password Changed',
            self::TYPE_TEMP_PASSWORD_SENT => 'Temporary Password Sent',
            self::TYPE_SETTINGS_UPDATED => 'Settings Updated',
            self::TYPE_EXCEL_IMPORTED => 'Excel File Imported',
            self::TYPE_SCRIPT_RETURNED => 'Script Returned',
            self::TYPE_PROVIDER_DELETED => 'Provider Deleted',
            self::TYPE_FAX_ADDED => 'Fax Number Added',
            self::TYPE_FAX_UPDATED => 'Fax Number Updated',
            self::TYPE_FAX_DELETED => 'Fax Number Deleted',
            self::TYPE_FAX_SENT => 'Fax Sent',
            self::TYPE_SCRIPT_DOWNLOADED => 'Script Downloaded',
            self::TYPE_SCRIPT_DELETED => 'Script Deleted',
        ];

        return $labels[$this->type] ?? ucwords(str_replace('_', ' ', $this->type));
    }

    /**
     * Get user type label for display
     */
    public function getUserTypeLabelAttribute()
    {
        $labels = [
            User::ROLE_ADMIN => 'Administrator',
            User::ROLE_OPERATOR => 'Operator',
            User::ROLE_PROVIDER => 'Provider',
        ];

        return $labels[$this->user_type] ?? $this->user_type;
    }
}
