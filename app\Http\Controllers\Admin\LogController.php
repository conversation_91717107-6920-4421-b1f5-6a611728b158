<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Resources\DataTableCollection;
use App\Models\SystemLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class LogController extends Controller
{
    public function index()
    {
        return view('settings.logs', [
            "page_title" => "System Logs",
        ]);
    }

    // public function indexWeb(GetDatatableRequest $request)
    // {
    //     $data = SystemLog::query()->with('user');

    //     // Handle search
    //     $search = $request->input('query.search', '');
    //     if ($search) {
    //         $query_search = "%" . $search . "%";
    //         $data->where(function ($query) use ($query_search) {
    //             $query->where('message', 'like', $query_search)
    //                 ->orWhere('username', 'like', $query_search)
    //                 ->orWhere('type', 'like', $query_search)
    //                 ->orWhere('user_type', 'like', $query_search)
    //                 ->orWhereRaw("DATE_FORMAT(timestamp, '%m/%d/%Y') LIKE ?", [$query_search])
    //                 ->orWhereRaw("DATE_FORMAT(timestamp, '%m/%d/%Y %h:%i %p') LIKE ?", [$query_search]);
    //         });
    //     }

    //     // Handle filters - try multiple parameter locations like other working controllers
    //     $typeFilter = $request->input('query.type') ?? $request->input('type');
    //     if (!empty($typeFilter)) {
    //         $data->where('type', $typeFilter);
    //     }

    //     $userTypeFilter = $request->input('query.user_type') ?? $request->input('user_type');
    //     if (!empty($userTypeFilter)) {
    //         $data->where('user_type', $userTypeFilter);
    //     }

    //     $dateFromFilter = $request->input('query.date_from') ?? $request->input('date_from');
    //     if (!empty($dateFromFilter)) {
    //         $data->whereDate('timestamp', '>=', $dateFromFilter);
    //     }

    //     $dateToFilter = $request->input('query.date_to') ?? $request->input('date_to');
    //     if (!empty($dateToFilter)) {
    //         $data->whereDate('timestamp', '<=', $dateToFilter);
    //     }

    //     // Handle sorting
    //     $sort_order = $request->input('sort.sort', 'desc');
    //     $sort_field = $request->input('sort.field', 'timestamp');

    //     $logColumns = Schema::getColumnListing((new SystemLog())->getTable());

    //     if (in_array($sort_field, $logColumns)) {
    //         $data->orderBy($sort_field, $sort_order);
    //     } else {
    //         $data->orderBy('timestamp', 'desc');
    //     }

    //     return new DataTableCollection($data->paginate(
    //         $request->pagination['perpage'],
    //         ['*'],
    //         'page',
    //         $request->pagination['page']
    //     ));
    // }

    public function indexWeb(GetDatatableRequest $request)
    {
        $data = SystemLog::query()->with('user');

        // Handle search
        $search = $request->input('query.search', '');
        if ($search) {
            $query_search = "%" . $search . "%";
            $data->where(function ($query) use ($query_search) {
                $query->where('message', 'like', $query_search)
                    ->orWhere('username', 'like', $query_search)
                    ->orWhere('type', 'like', $query_search)
                    ->orWhere('user_type', 'like', $query_search);
            });
        }

        // Handle filters - check multiple possible locations for filter data
        $allParams = $request->all();

        // Type filter - check all possible parameter locations
        $typeFilter = $request->input('query.type') ??
            $request->input('type') ??
            ($allParams['query']['type'] ?? null);
        if (!empty($typeFilter)) {
            $data->where('type', $typeFilter);
        }

        // User type filter - check all possible parameter locations
        $userTypeFilter = $request->input('query.user_type') ??
            $request->input('user_type') ??
            ($allParams['query']['user_type'] ?? null);
        if (!empty($userTypeFilter)) {
            $data->where('user_type', $userTypeFilter);
        }

        // Date range filter - check all possible parameter locations
        $dateFromFilter = $request->input('query.date_from') ??
            $request->input('date_from') ??
            ($allParams['query']['date_from'] ?? null);
        if (!empty($dateFromFilter)) {
            $data->whereDate('timestamp', '>=', $dateFromFilter);
        }

        $dateToFilter = $request->input('query.date_to') ??
            $request->input('date_to') ??
            ($allParams['query']['date_to'] ?? null);
        if (!empty($dateToFilter)) {
            $data->whereDate('timestamp', '<=', $dateToFilter);
        }

        // Handle sorting
        $sort_order = $request->input('sort.sort', 'desc');
        $sort_field = $request->input('sort.field', 'timestamp');

        $logColumns = Schema::getColumnListing((new SystemLog())->getTable());

        if (in_array($sort_field, $logColumns)) {
            $data->orderBy($sort_field, $sort_order);
        } else {
            $data->orderBy('timestamp', 'desc');
        }

        return new DataTableCollection($data->paginate(
            $request->pagination['perpage'],
            ['*'],
            'page',
            $request->pagination['page']
        ));
    }

    public function getLogTypes()
    {
        $types = [
            SystemLog::TYPE_SYSTEM_ERROR => 'System Error',
            SystemLog::TYPE_USER_ACTION => 'User Action',
            SystemLog::TYPE_LOGIN => 'Login',
            SystemLog::TYPE_LOGOUT => 'Logout',
            SystemLog::TYPE_SCRIPT_CREATED => 'Script Created',
            SystemLog::TYPE_SCRIPT_EDITED => 'Script Edited',
            SystemLog::TYPE_SCRIPT_SIGNED => 'Script Signed',
            SystemLog::TYPE_SCRIPT_SENT => 'Script Sent',
            SystemLog::TYPE_PROVIDER_CREATED => 'Provider Created',
            SystemLog::TYPE_PROVIDER_EDITED => 'Provider Edited',
            SystemLog::TYPE_PROVIDER_DEACTIVATED => 'Provider Deactivated',
            SystemLog::TYPE_PROVIDER_ACTIVATED => 'Provider Activated',
            SystemLog::TYPE_STAFF_CREATED => 'Staff Created',
            SystemLog::TYPE_STAFF_EDITED => 'Staff Edited',
            SystemLog::TYPE_STAFF_DEACTIVATED => 'Staff Deactivated',
            SystemLog::TYPE_STAFF_ACTIVATED => 'Staff Activated',
            SystemLog::TYPE_STAFF_DELETED => 'Staff Deleted',
            SystemLog::TYPE_PASSWORD_CHANGED => 'Password Changed',
            SystemLog::TYPE_TEMP_PASSWORD_SENT => 'Temporary Password Sent',
            SystemLog::TYPE_SETTINGS_UPDATED => 'Settings Updated',
            SystemLog::TYPE_EXCEL_IMPORTED => 'Excel File Imported',
            SystemLog::TYPE_SCRIPT_RETURNED => 'Script Returned',
            SystemLog::TYPE_PROVIDER_DELETED => 'Provider Deleted',
            SystemLog::TYPE_FAX_ADDED => 'Fax Number Added',
            SystemLog::TYPE_FAX_UPDATED => 'Fax Number Updated',
            SystemLog::TYPE_FAX_DELETED => 'Fax Number Deleted',
            SystemLog::TYPE_FAX_SENT => 'Fax Sent',
            SystemLog::TYPE_SCRIPT_DOWNLOADED => 'Script Downloaded',
            SystemLog::TYPE_SCRIPT_DELETED => 'Script Deleted',
        ];

        return response()->json($types);
    }

    public function getUserTypes()
    {
        $userTypes = [
            User::ROLE_ADMIN => 'Administrator',
            User::ROLE_OPERATOR => 'Operator',
            User::ROLE_PROVIDER => 'Provider',
        ];

        return response()->json($userTypes);
    }
}
