<?php

namespace App\Jobs;

use App\Models\FaxNumbers;
use App\Models\ImportFile;
use App\Services\LogService;
use App\Traits\FaxManager;
use App\Traits\FileManager;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SendFilesToFaxJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use FaxManager, FileManager;

    public $import_file_ids;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($import_file_ids)
    {
        $this->import_file_ids = $import_file_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('SendFilesToFaxJob starting', [
            'import_file_ids' => $this->import_file_ids,
            'count' => count($this->import_file_ids)
        ]);

        // Get only the files that match our IDs and have the correct status
        $import_files = ImportFile::with('import')
            ->where('status', ImportFile::STATUS_PENDING_DISPATCH)
            ->whereIn('id', $this->import_file_ids)
            ->get();

        Log::info('Files retrieved for processing', [
            'count' => $import_files->count(),
            'file_ids' => $import_files->pluck('id')->toArray()
        ]);

        $filepathArray = [];

        // Update status to Pending Dispatch for all files
        foreach ($import_files as $import_file) {
            if ($import_file->status !== ImportFile::STATUS_PENDING_DISPATCH) {
                continue;
            }

            Log::info('Updated status to Pending Dispatch', ['status' => $import_file->status]);

            $file = $import_file->file_path;
            if (Storage::exists($file)) {
                $filepathArray[] = $file;
            }
        }

        // Check if we have files to process
        if (empty($filepathArray)) {
            Log::warning('No files found to send via fax');
            return;
        }

        $filesArray = [];

        // Merge PDFs if there are multiple files
        Log::info('Merging multiple PDFs', ['file_count' => count($filepathArray)]);

        // Merge PDFs using our trait
        $mergeResult = $this->mergePdfs($filepathArray, 'temp/fax_merged_pdfs');


        if ($mergeResult && isset($mergeResult['full_path'])) {
            // Upload the merged PDF
            $response = $this->uploadFiles($mergeResult['full_path']);

            if (isset($response['path'])) {
                $filesArray[] = $response['path'];
                Log::info('Successfully merged and uploaded PDFs', [
                    'merged_count' => $mergeResult['merged_count'],
                    'merged_file' => $mergeResult['path']
                ]);

                // // Delete the temporary merged PDF file after successful upload
                // if (Storage::exists($mergeResult['path'])) {
                //     Storage::delete($mergeResult['path']);
                //     Log::info('Deleted temporary merged PDF file', [
                //         'temp_file' => $mergeResult['path']
                //     ]);
                // }
            } else {
                Log::error('Failed to upload merged PDF', [
                    'merged_file' => $mergeResult['path']
                ]);

                // Delete the temporary file even if upload failed
                // if (Storage::exists($mergeResult['path'])) {
                //     Storage::delete($mergeResult['path']);
                //     Log::info('Deleted temporary merged PDF file after failed upload', [
                //         'temp_file' => $mergeResult['path']
                //     ]);
                // }
            }
        } else {
            Log::error('Failed to merge PDFs, falling back to individual uploads', ['merged_result', $mergeResult]);

            // If merging fails, upload files individually
            foreach ($filepathArray as $file) {
                $response = $this->uploadFiles($file);

                if (isset($response['path'])) {
                    $filesArray[] = $response['path'];
                }
            }
        }


        try {
            if (!empty($filesArray)) {
                $userId = config('fax.user_id');
                $fax_numbers = FaxNumbers::where('is_active', 1)->get()->pluck('numbers')->toArray();
                // Add + prefix to each fax number if not already present
                $to = array_map(function ($number) {
                    return str_starts_with($number, '+') ? $number : '+' . $number;
                }, $fax_numbers);

                Log::info('Fax numbers retrieved with + prefix', [
                    'count' => count($to),
                    'numbers' => $to
                ]);

                // $to = config('fax.to_number');
                $from = config('fax.from_number');

                try {
                    $sendFax = $this->sendFax($to, $filesArray, $from, $userId);

                    if ($sendFax) {
                        foreach ($import_files as $import_file) {
                            $import_file->status = ImportFile::STATUS_SENT;
                            $import_file->sent_at = now();
                            $import_file->save();
                            Log::info('Updated status to sent', ['status' => $import_file->status]);

                            // Log fax sent for each script
                            LogService::logFaxSent([
                                'patient_name' => $import_file->first_name . ' ' . $import_file->last_name,
                                'medication' => $import_file->medication,
                                'script_id' => $import_file->id,
                                'status' => $import_file->status
                            ], implode(', ', $to), [
                                'from' => $from,
                                'files_count' => count($filesArray)
                            ]);
                        }
                        Log::info('Fax sent successfully', [
                            'to' => $to,
                            'from' => $from,
                            'files' => $filesArray
                        ]);
                    } else {
                        // Revert status back to Pending Approval if fax sending fails
                        foreach ($import_files as $import_file) {
                            $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                            $import_file->save();
                            Log::info('Updated status to Pending approval', ['status' => $import_file->status]);
                        }

                        Log::error('Failed to send fax', [
                            'to' => $to,
                            'from' => $from,
                            'files' => $filesArray
                        ]);
                    }
                } catch (\App\Exceptions\CustomException $e) {
                    // Handle Fax API error response and revert status
                    foreach ($import_files as $import_file) {
                        $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                        $import_file->save();
                        Log::info('Updated status to Pending approval', ['status' => $import_file->status]);
                    }

                    Log::error('Fax API Error', [
                        'message' => $e->getMessage(),
                        'code' => $e->getCode()
                    ]);
                }
            }
        } catch (\Throwable $th) {
            // Revert status back to Pending Approval if there's an exception
            foreach ($import_files as $import_file) {
                $import_file->status = ImportFile::STATUS_PENDING_APPROVAL;
                $import_file->save();
            }

            Log::error('Fax sending exception', [
                'message' => $th->getMessage(),
                'file' => $th->getFile(),
                'line' => $th->getLine()
            ]);
        } finally {
            // Final cleanup - ensure all temporary merged PDF files are deleted
            $this->cleanupTempMergedPdfs();
        }
    }

    /**
     * Clean up any temporary merged PDF files
     *
     * @return void
     */
    private function cleanupTempMergedPdfs()
    {
        try {
            $tempDirectory = 'temp/fax_merged_pdfs';

            // Check if the directory exists
            if (Storage::exists($tempDirectory)) {
                // Get all files in the directory
                $files = Storage::files($tempDirectory);

                // Delete each file
                foreach ($files as $file) {
                    // Only delete PDF files
                    if (strtolower(pathinfo($file, PATHINFO_EXTENSION)) === 'pdf') {
                        Storage::delete($file);
                        Log::info('Cleaned up temporary PDF file', ['file' => $file]);
                    }
                }

                // Optionally, you can also delete the directory if it's empty
                if (empty(Storage::files($tempDirectory))) {
                    Storage::deleteDirectory($tempDirectory);
                    Log::info('Removed empty temporary directory', ['directory' => $tempDirectory]);
                }
            }
        } catch (\Throwable $th) {
            // Just log the error but don't throw it - this is cleanup code
            Log::warning('Error during cleanup of temporary PDF files', [
                'message' => $th->getMessage()
            ]);
        }
    }
}
